/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Service with small utility methods
 */
angular.module('flowableModeler').service('UtilityService', [ '$window', '$document', '$timeout', function ($window, $document, $timeout) {

    this.scrollToElement = function(elementId) {
        $timeout(function() {
            var someElement = angular.element(document.getElementById(elementId))[0];
            if (someElement) {
                if (someElement.getBoundingClientRect().top > $window.innerHeight) {
                    $document.scrollToElement(someElement, 0, 1000);
                }
            }
        });
    };

}]);