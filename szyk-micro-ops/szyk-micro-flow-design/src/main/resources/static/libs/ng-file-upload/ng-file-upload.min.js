/*! 4.1.0 */
!function(){function a(a,b){window.XMLHttpRequest.prototype[a]=b(window.XMLHttpRequest.prototype[a])}function b(a,b,c,d,g,h){function i(){return"input"===b[0].tagName.toLowerCase()&&b.attr("type")&&"file"===b.attr("type").toLowerCase()}function j(b){if(!p){p=!0;try{for(var i=b.__files_||b.target&&b.target.files,j=[],k=[],l=0;l<i.length;l++){var m=i.item(l);f(a,g,c,m,b)?j.push(m):k.push(m)}e(g,h,a,d,c,o,j,k,b),0==j.length&&(b.target.value=j)}finally{p=!1}}}function k(d){c.ngfMultiple&&d.attr("multiple",g(c.ngfMultiple)(a)),g(c.ngfMultiple)(a)||d.attr("multiple",void 0),c.accept&&d.attr("accept",c.accept),c.ngfCapture&&d.attr("capture",g(c.ngfCapture)(a)),c.ngfDisabled&&d.attr("disabled",g(c.ngfDisabled)(a));for(var e=0;e<b[0].attributes.length;e++){var f=b[0].attributes[e];"type"!==f.name&&"class"!==f.name&&"id"!==f.name&&"style"!==f.name&&d.attr(f.name,f.value)}}function l(){if(!b.attr("disabled")){var a=angular.element('<input type="file">');return k(a),i()?(b.replaceWith(a),b=a):(a.css("display","none").attr("tabindex","-1").attr("__ngf_gen__",!0),b.__ngf_ref_elem__&&b.__ngf_ref_elem__.remove(),b.__ngf_ref_elem__=a,document.body.appendChild(a[0])),a}}function m(b){e(g,h,a,d,c,o,[],[],b,!0)}function n(a){function c(){d[0].click(),i()&&(b.bind("click touchend",n),a.preventDefault())}a.preventDefault();var d=l(a);d&&(d.bind("change",j),m(a),navigator.userAgent.toLowerCase().match(/android/)?setTimeout(function(){c()},0):c())}var o=c.ngfChange||c.ngfSelect&&c.ngfSelect.indexOf("(")>0,p=!1;window.FileAPI&&window.FileAPI.ngfFixIE?window.FileAPI.ngfFixIE(b,l,k,j,m):b.bind("click touchend",n)}function c(a,b,c,g,h,i,j){function k(a,b,c){var d=!0,e=c.dataTransfer.items;if(null!=e)for(var g=0;g<e.length&&d;g++)d=d&&("file"==e[g].kind||""==e[g].kind)&&f(a,h,b,e[g],c);var i=h(b.ngfDragOverClass)(a,{$event:c});return i&&(i.delay&&(q=i.delay),i.accept&&(i=d?i.accept:i.reject)),i||b.ngfDragOverClass||"dragover"}function l(b,d,e,g){function k(d){f(a,h,c,d,b)?m.push(d):n.push(d)}function l(a,b,c){if(null!=b)if(b.isDirectory){var d=(c||"")+b.name;k({name:b.name,type:"directory",path:d});var e=b.createReader(),f=[];p++;var g=function(){e.readEntries(function(d){try{if(d.length)f=f.concat(Array.prototype.slice.call(d||[],0)),g();else{for(var e=0;e<f.length;e++)l(a,f[e],(c?c:"")+b.name+"/");p--}}catch(h){p--,console.error(h)}},function(){p--})};g()}else p++,b.file(function(a){try{p--,a.path=(c?c:"")+a.name,k(a)}catch(b){p--,console.error(b)}},function(){p--})}var m=[],n=[],o=b.dataTransfer.items,p=0;if(o&&o.length>0&&"file"!=j.protocol())for(var q=0;q<o.length;q++){if(o[q].webkitGetAsEntry&&o[q].webkitGetAsEntry()&&o[q].webkitGetAsEntry().isDirectory){var r=o[q].webkitGetAsEntry();if(r.isDirectory&&!e)continue;null!=r&&l(m,r)}else{var s=o[q].getAsFile();null!=s&&k(s)}if(!g&&m.length>0)break}else{var t=b.dataTransfer.files;if(null!=t)for(var q=0;q<t.length&&(k(t.item(q)),g||!(m.length>0));q++);}var u=0;!function v(a){i(function(){if(p)10*u++<2e4&&v(10);else{if(!g&&m.length>1){for(q=0;"directory"==m[q].type;)q++;m=[m[q]]}d(m,n)}},a||0)}()}var m=d();if(c.dropAvailable&&i(function(){a[c.dropAvailable]?a[c.dropAvailable].value=m:a[c.dropAvailable]=m}),!m)return 1==h(c.ngfHideOnDropNotAvailable)(a)&&b.css("display","none"),void 0;var n,o=null,p=h(c.ngfStopPropagation),q=1,r=(h(c.ngfAccept),h(c.ngfDisabled));b[0].addEventListener("dragover",function(d){if(!r(a)){if(d.preventDefault(),p(a)&&d.stopPropagation(),navigator.userAgent.indexOf("Chrome")>-1){var e=d.dataTransfer.effectAllowed;d.dataTransfer.dropEffect="move"===e||"linkMove"===e?"move":"copy"}i.cancel(o),a.actualDragOverClass||(n=k(a,c,d)),b.addClass(n)}},!1),b[0].addEventListener("dragenter",function(b){r(a)||(b.preventDefault(),p(a)&&b.stopPropagation())},!1),b[0].addEventListener("dragleave",function(){r(a)||(o=i(function(){b.removeClass(n),n=null},q||1))},!1),b[0].addEventListener("drop",function(d){r(a)||(d.preventDefault(),p(a)&&d.stopPropagation(),b.removeClass(n),n=null,l(d,function(b,f){e(h,i,a,g,c,c.ngfChange||c.ngfDrop&&c.ngfDrop.indexOf("(")>0,b,f,d)},0!=h(c.ngfAllowDir)(a),c.multiple||h(c.ngfMultiple)(a)))},!1)}function d(){var a=document.createElement("div");return"draggable"in a&&"ondrop"in a}function e(a,b,c,d,e,f,g,h,i,j){function k(){d&&(a(e.ngModel).assign(c,g),b(function(){d&&d.$setViewValue(null!=g&&0==g.length?null:g)})),e.ngModelRejected&&a(e.ngModelRejected).assign(c,h),f&&a(f)(c,{$files:g,$rejectedFiles:h,$event:i})}j?k():b(function(){k()})}function f(a,b,c,d,e){var f=b(c.ngfAccept),h=b(c.ngfMaxSize)(a)||9007199254740991,i=b(c.ngfMinSize)(a)||-1,j=f(a,{$file:d,$event:e}),k=!1;if(null!=j&&angular.isString(j)){var l=new RegExp(g(j),"gi");k=null!=d.type&&d.type.match(l)||null!=d.name&&d.name.match(l)}return(null==j||k)&&(null==d.size||d.size<h&&d.size>i)}function g(a){if(a.length>2&&"/"===a[0]&&"/"===a[a.length-1])return a.substring(1,a.length-1);var b=a.split(","),c="";if(b.length>1)for(var d=0;d<b.length;d++)c+="("+g(b[d])+")",d<b.length-1&&(c+="|");else 0==a.indexOf(".")&&(a="*"+a),c="^"+a.replace(new RegExp("[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\-]","g"),"\\$&")+"$",c=c.replace(/\\\*/g,".*").replace(/\\\?/g,".");return c}var h;window.XMLHttpRequest&&!window.XMLHttpRequest.__isFileAPIShim&&a("setRequestHeader",function(a){return function(b,c){if("__setXHR_"===b){var d=c(this);d instanceof Function&&d(this)}else a.apply(this,arguments)}});var i=angular.module("ngFileUpload",[]);i.version="4.1.0",i.service("Upload",["$http","$q","$timeout",function(a,b,c){function d(d){d.method=d.method||"POST",d.headers=d.headers||{},d.transformRequest=d.transformRequest||function(b,c){return window.ArrayBuffer&&b instanceof window.ArrayBuffer?b:a.defaults.transformRequest[0](b,c)};var e=b.defer(),f=e.promise;return d.headers.__setXHR_=function(){return function(a){a&&(d.__XHR=a,d.xhrFn&&d.xhrFn(a),a.upload.addEventListener("progress",function(a){a.config=d,e.notify?e.notify(a):f.progress_fn&&c(function(){f.progress_fn(a)})},!1),a.upload.addEventListener("load",function(a){a.lengthComputable&&(a.config=d,e.notify?e.notify(a):f.progress_fn&&c(function(){f.progress_fn(a)}))},!1))}},a(d).then(function(a){e.resolve(a)},function(a){e.reject(a)},function(a){e.notify(a)}),f.success=function(a){return f.then(function(b){a(b.data,b.status,b.headers,d)}),f},f.error=function(a){return f.then(null,function(b){a(b.data,b.status,b.headers,d)}),f},f.progress=function(a){return f.progress_fn=a,f.then(null,null,function(b){a(b)}),f},f.abort=function(){return d.__XHR&&c(function(){d.__XHR.abort()}),f},f.xhr=function(a){return d.xhrFn=function(b){return function(){b&&b.apply(f,arguments),a.apply(f,arguments)}}(d.xhrFn),f},f}this.upload=function(a){return a.headers=a.headers||{},a.headers["Content-Type"]=void 0,a.transformRequest=a.transformRequest?angular.isArray(a.transformRequest)?a.transformRequest:[a.transformRequest]:[],a.transformRequest.push(function(b){var c=new FormData,d={};for(h in a.fields)a.fields.hasOwnProperty(h)&&(d[h]=a.fields[h]);if(b&&(d.data=b),a.formDataAppender)for(h in d)d.hasOwnProperty(h)&&a.formDataAppender(c,h,d[h]);else for(h in d)if(d.hasOwnProperty(h)){var e=d[h];void 0!==e&&(angular.isDate(e)&&(e=e.toISOString()),angular.isString(e)?c.append(h,e):a.sendObjectsAsJsonBlob&&angular.isObject(e)?c.append(h,new Blob([e],{type:"application/json"})):c.append(h,JSON.stringify(e)))}if(null!=a.file){var f=a.fileFormDataName||"file";if(angular.isArray(a.file))for(var g=angular.isString(f),i=0;i<a.file.length;i++)c.append(g?f:f[i],a.file[i],a.fileName&&a.fileName[i]||a.file[i].name);else c.append(f,a.file,a.fileName||a.file.name)}return c}),d(a)},this.http=function(a){return d(a)}}]),i.directive("ngfSelect",["$parse","$timeout","$compile",function(a,c,d){return{restrict:"AEC",require:"?ngModel",link:function(e,f,g,h){b(e,f,g,h,a,c,d)}}}]),i.directive("ngfDrop",["$parse","$timeout","$location",function(a,b,d){return{restrict:"AEC",require:"?ngModel",link:function(e,f,g,h){c(e,f,g,h,a,b,d)}}}]),i.directive("ngfNoFileDrop",function(){return function(a,b){d()&&b.css("display","none")}}),i.directive("ngfDropAvailable",["$parse","$timeout",function(a,b){return function(c,e,f){if(d()){var g=a(f.ngfDropAvailable);b(function(){g(c),g.assign&&g.assign(c,!0)})}}}]),i.directive("ngfSrc",["$parse","$timeout",function(a,b){return{restrict:"AE",link:function(a,c,d){window.FileReader&&a.$watch(d.ngfSrc,function(a){a?b(function(){var d=new FileReader;d.readAsDataURL(a),d.onload=function(a){b(function(){c.attr("src",a.target.result)})}}):c.attr("src","")})}}}])}();