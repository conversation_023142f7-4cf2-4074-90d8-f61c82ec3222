
<span ng-if="property.value.assignment.type != 'idm' && property.value.assignment.assignee">{{'PROPERTY.ASSIGNMENT.ASSIGNEE_DISPLAY' | translate:property.value.assignment }} </span>
<span ng-if="property.value.assignment.type != 'idm' && property.value.assignment.candidateUsers.length > 0">{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY' | translate:property.value.assignment.candidateUsers}} </span>
<span ng-if="property.value.assignment.type != 'idm' && property.value.assignment.candidateGroups.length > 0">{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY' | translate:property.value.assignment.candidateGroups}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.assignee && property.value.assignment.idm.assignee.id">{{'PROPERTY.ASSIGNMENT.USER_IDM_DISPLAY' | translate:property.value.assignment.idm.assignee }} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.assignee && !property.value.assignment.idm.assignee.id">{{'PROPERTY.ASSIGNMENT.USER_IDM_EMAIL_DISPLAY' | translate:property.value.assignment.idm.assignee }} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.assigneeField && property.value.assignment.idm.assigneeField.id">{{'PROPERTY.ASSIGNMENT.USER_IDM_FIELD_DISPLAY' | translate:property.value.assignment.idm.assigneeField }} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateUsers && property.value.assignment.idm.candidateUsers.length > 0 && (!property.value.assignment.idm.candidateUserFields || property.value.assignment.idm.candidateUserFields.length === 0)">{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY' | translate:property.value.assignment.idm.candidateUsers}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateGroups && property.value.assignment.idm.candidateGroups.length > 0 && (!property.value.assignment.idm.candidateGroupFields || property.value.assignment.idm.candidateGroupFields.length === 0)">{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY' | translate:property.value.assignment.idm.candidateGroups}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateUserFields && property.value.assignment.idm.candidateUserFields.length > 0 && (!property.value.assignment.idm.candidateUsers || property.value.assignment.idm.candidateUsers.length === 0)">{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY' | translate:property.value.assignment.idm.candidateUserFields}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateGroupFields && property.value.assignment.idm.candidateGroupFields.length > 0 && (!property.value.assignment.idm.candidateGroups || property.value.assignment.idm.candidateGroups.length === 0)">{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY' | translate:property.value.assignment.idm.candidateGroupFields}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateUserFields && property.value.assignment.idm.candidateUserFields.length > 0 && property.value.assignment.idm.candidateUsers && property.value.assignment.idm.candidateUsers.length > 0">{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY' | translate:property.value.assignment.idm.candidateUserFields.concat(property.value.assignment.idm.candidateUsers)}} </span>
<span ng-if="property.value.assignment.type == 'idm' && property.value.assignment.idm.candidateGroupFields && property.value.assignment.idm.candidateGroupFields.length > 0 && property.value.assignment.idm.candidateGroups && property.value.assignment.idm.candidateGroups.length > 0">{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY' | translate:property.value.assignment.idm.candidateGroupFields.concat(property.value.assignment.idm.candidateGroups)}} </span>
<span ng-if="property.value.assignment.type != 'idm' && !property.value.assignment.assignee && (!property.value.assignment.candidateUsers || property.value.assignment.candidateUsers.length == 0) && (!property.value.assignment.candidateGroups || property.value.assignment.candidateGroups.length == 0)" translate>PROPERTY.ASSIGNMENT.EMPTY</span>
<span ng-if="property.value.assignment.type == 'idm' && !property.value.assignment.idm.assignee && !property.value.assignment.idm.assigneeField && (!property.value.assignment.idm.candidateUsers || property.value.assignment.idm.candidateUsers.length == 0) && (!property.value.assignment.idm.candidateUserFields || property.value.assignment.idm.candidateUserFields.length == 0) && (!property.value.assignment.idm.candidateGroups || property.value.assignment.idm.candidateGroups.length == 0) && (!property.value.assignment.idm.candidateGroupFields || property.value.assignment.idm.candidateGroupFields.length == 0)" translate>PROPERTY.ASSIGNMENT.IDM_EMPTY</span>