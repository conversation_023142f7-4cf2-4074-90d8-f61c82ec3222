<div class="subheader" id="list-header">
	<div class="fixed-container">
	    <div class="btn-group pull-right">
	        <button type="button" class="btn btn-default" ng-click="createDecisionTable()" ng-disabled="readOnly" translate>DECISION-TABLES-LIST.ACTION.CREATE</button>
	        <button type="button" class="btn btn-default" ng-click="importDecisionTable()" ng-disabled="readOnly" translate>DECISION-TABLES-LIST.ACTION.IMPORT</button>
	    </div>
	    <h2>{{'DECISION-TABLES-LIST.TITLE' | translate}}</h2>
	</div>
</div>
<div class="container-fluid content" id="list-container" auto-height offset="40">
        <div class="col-xs-2 filter-wrapper">
            <div class="input-group">
                <span class="input-group-addon"> <i
                    class="glyphicon glyphicon-search"></i>
                </span> <input type="text" ng-model="model.pendingFilterText" class="form-control" ng-change="filterDelayed()"
                    placeholder="{{'DECISION-TABLES-LIST.SEARCH-PLACEHOLDER' | translate}}">
            </div>
            <ul class="filter-list">
                <li ng-repeat="filter in model.filters" ng-class="{'current' : filter.id == model.activeFilter.id}">
                    <a ng-click="activateFilter(filter)">{{'DECISION-TABLES-LIST.FILTER.' + filter.labelKey | translate}}</a>
                </li>
            </ul>
        </div>
        
        <div class="col-xs-10 item-wrapper" id="list-items">
            <div class="dropdown-subtle pull-right">
                <div class="btn-group btn-group-sm" activiti-fix-dropdown-bug>
                    <button type="button" class="btn btn-default dropdown-toggle"
                        data-toggle="dropdown">{{'DECISION-TABLES-LIST.SORT.' + model.activeSort.labelKey | translate}} <i class="caret"></i></button>
                    <ul class="dropdown-menu pull-right">
                        <li ng-repeat="sort in model.sorts">
                            <a ng-click="activateSort(sort)">{{'DECISION-TABLES-LIST.SORT.' + sort.labelKey | translate}}</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="message clearfix">
                <div class="loading pull-left" ng-show="model.loading">
                    <div class="l1"></div><div class="l2"></div><div class="l2"></div>
                </div>
                <div ng-if="!model.loading">
                    <span ng-if="model.decisionTables.size > 1">{{'DECISION-TABLES-LIST.FILTER.' + model.activeFilter.labelKey + '-COUNT' | translate:model.decisionTables}}</span>
                    <span ng-if="model.decisionTables.size == 1">{{'DECISION-TABLES-LIST.FILTER.' + model.activeFilter.labelKey + '-ONE' | translate}}</span>
                    <span ng-if="model.decisionTables.size == 0 && (!model.filterText || model.filterText == '')">{{'DECISION-TABLES-LIST.FILTER.' + model.activeFilter.labelKey + '-EMPTY' | translate}}</span>
                    <span ng-if="model.decisionTables.size > 0 && model.filterText !='' && model.filterText !== undefined">{{'DECISION-TABLES-LIST.FILTER.FILTER-TEXT' | translate:model}}</span>
                    <span ng-if="model.decisionTables.size == 0 && model.filterText !='' && model.filterText !== undefined">{{'DECISION-TABLES-LIST.FILTER.FILTER-TEXT-EMPTY' | translate:model}}</span>
                </div>
            </div>
            
            <div class="create-inline" ng-if="model.decisionTables.size == 0 && (!model.filterText || model.filterText == '')">
                <span>{{'DECISION-TABLES-LIST.FILTER.' + model.activeFilter.labelKey + '-EMPTY' | translate}}</span>
                <span><button type="button" class="btn btn-default" ng-click="createDecisionTable()" ng-disabled="readOnly">
                    <i class="glyphicon glyphicon-plus-sign"></i>{{'DECISION-TABLES-LIST.ACTION.CREATE-INLINE' | translate}}
                </button></span>
            </div>
            
            <div class="item fadein" ng-repeat="decisionTable in model.decisionTables.data track by $index">
                <div class="item-box" ng-style="{'background-image': 'url(\'' + getModelThumbnailUrl(decisionTable.id, imageVersion) + '\')'}" ng-click="showDecisionTableDetails(decisionTable);">
                    <div class="actions">
                        <span class="badge">v{{decisionTable.version}}</span>
                        <div class="btn-group pull-right">
                            <button type="button" ng-click="showDecisionTableDetails(decisionTable); $event.stopPropagation();" class="btn btn-default" title="{{'DECISION-TABLE.ACTION.DETAILS' | translate}}">
                                <i class="glyphicon glyphicon-search"></i>
                            </button>
                            <button type="button" ng-click="editDecisionTableDetails(decisionTable); $event.stopPropagation();" class="btn btn-default" title="{{'DECISION-TABLE.ACTION.OPEN-IN-EDITOR' | translate}}">
                                <i class="glyphicon glyphicon-edit"></i>
                            </button>
                        </div>
                    </div>
                    <div class="details">
                        <h3 class="truncate" title="{{decisionTable.name}}">
                            {{decisionTable.name}}
                        </h3>
                        <div class="basic-details truncate">
                            <span><i class="glyphicon glyphicon-user"></i> {{decisionTable.createdBy}}</span> <span title="{{decisionTable.lastUpdated | dateformat:'LLLL'}}"><i class="glyphicon glyphicon-pencil"></i> {{decisionTable.lastUpdated | dateformat}}</span>
                        </div>
                        <p>{{decisionTable.description}}</p>
                    </div>
                </div>
            </div>
            
            <div class="show-more" ng-if="model.decisionTables.data.length < model.decisionTables.total">
                <a>{{'DECISION-TABLES-LIST.ACTION.SHOW-MORE' | translate}}</a>
            </div>
        </div>
</div>
