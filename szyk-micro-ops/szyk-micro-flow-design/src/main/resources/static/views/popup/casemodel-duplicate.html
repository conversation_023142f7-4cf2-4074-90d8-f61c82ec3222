<div class="modal" ng-controller="DuplicateCaseModelCtrl">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header"><h2>{{'CASE.POPUP.DUPLICATE-TITLE' | translate}}</h2></div>
			<div class="modal-body">
				<p>{{'CASE.POPUP.DUPLICATE-DESCRIPTION' | translate}}</p>
				<div ng-if="model.errorMessage && model.errorMessage.length > 0" class="alert error" style="font-size: 14px; margin-top:20px">
                  <div class="popup-error" style="font-size: 14px">
                    <span class="glyphicon glyphicon-remove-circle"></span>
                    <span>{{model.errorMessage}}</span>
                  </div>
                </div>
				<div class="form-group">
				    <label for="newCaseName">{{'CASE.NAME' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control"
			               id="newCaseName" ng-model="model.caseModel.name" ui-keypress="{13:'ok()'}" auto-focus editor-input-check>
				</div>
				<div class="form-group">
                    <label for="newCaseModelKey">{{'CASE.KEY' | translate}}</label>
                    <input ng-disabled="model.loading" type="text" class="form-control"
                           id="newCaseModelKey" ng-model="model.caseModel.key" editor-input-check>
                </div>
				<div class="form-group">
				    <label for="newCaseDescription">{{'CASE.DESCRIPTION' | translate}}</label>
					<textarea ng-disabled="model.loading" class="form-control" id="newCaseDescription" rows="5" ng-model="model.caseModel.description"></textarea>
				</div>
			</div>
			
			<div class="modal-footer">
				<div class="pull-right">
					<button type="button" class="btn btn-sm btn-default" ng-click="cancel()" ng-disabled="model.loading">
						{{'GENERAL.ACTION.CANCEL' | translate}}
					</button>
					<button type="button" class="btn btn-sm btn-default" ng-click="ok()"ng-disabled="model.loading || !model.caseModel.name">
						{{'CASE.ACTION.DUPLICATE-CONFIRM' | translate}}
					</button>
				</div>
				<div class="loading pull-right" ng-show="model.loading">
					<div class="l1"></div><div class="l2"></div><div class="l2"></div>
				</div>
			</div>
		</div>
	</div>
</div>