<!doctype html>
<!--[if lt IE 7]>
<html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>
<html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>
<html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Szyk 流程设计器</title>
    <link rel="icon" type="image/ico" href="favicon.ico">
    <meta name="description" content="">
    <meta name="viewport"
          content="initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, width=device-width">
    <meta name="theme-color" content="#ffffff">

    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png?v=Om5N75Y123">
    <link rel="icon" type="image/png" href="images/favicon-32x32.png?v=Om5N75Y123" sizes="32x32">
    <link rel="icon" type="image/png" href="images/favicon-16x16.png?v=Om5N75Y123" sizes="16x16">
    <link rel="manifest" href="manifest.json">
    <link rel="mask-icon" href="images/safari-pinned-tab.svg?v=Om5N75Y123" color="#506d75">
    <link rel="shortcut icon" href="favicon.ico?v=Om5N75Y123">
    <link rel="Stylesheet" href="libs/ui-grid_3.0.0/ui-grid.css" type="text/css"/>
    <link rel="Stylesheet" href="libs/handsontable_0.31.2/handsontable.full.min.css" type="text/css"/>

    <!-- build:css styles/3thparty.css -->
    <link rel="stylesheet" href="libs/bootstrap_3.1.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="libs/bootstrap-tour_0.9.1/bootstrap-tour.min.css"/>
    <link rel="stylesheet" href="libs/angular-spectrum-colorpicker_1.0.13/spectrum.css"/>
    <!-- endbuild -->

    <link rel="Stylesheet" media="screen" href="editor-app/editor/css/editor.css?v=2" type="text/css"/>
    <link rel="stylesheet" href="editor-app/css/style.css?v=2" type="text/css"/>

    <!-- build:css styles/style.css -->
    <link href="styles/common/style.css" rel="stylesheet">
    <link href="styles/common/style-retina.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/style-editor.css">
    <!-- endbuild -->

</head>
<body ng-app="flowableModeler" ng-cloak>

    <div class="navbar navbar-fixed-top navbar-inverse" role="navigation" id="main-header">
        <div class="fixed-container">
            <div class="navbar-header">
                <a ng-click="backToLanding()" class="landing-logo" ng-if="account != null && account != undefined" title="{{'GENERAL.MAIN-TITLE' | translate}}">
                    <span style="color: #fff;font-size: 18px;margin-left: 20px;">Szyk 流程设计器</span>
                    <!--<img ng-src="{{appResourceRoot}}images/flowable-logo.png" ng-srcset="{{appResourceRoot}}images/flowable-logo.png 1x, {{appResourceRoot}}images/<EMAIL> 2x">-->
                </a>
                <ul class="nav navbar-nav" id="main-nav" ng-show="authenticated">
                    <li ng-class="{'active' : item.id == mainPage.id}" ng-repeat="item in mainNavigation">
                        <a ng-click="setMainPage(item)">{{item.title | translate}}</a>
                    </li>
                </ul>
            </div>
            <div class="pull-right {{currentAppDefinition.definition.theme}}"
                 ng-class="{'app-indicator': currentAppDefinition}" ng-if="authenticated" ng-cloack>
                <span ng-if="currentAppDefinition.definition.theme">
                    {{currentAppDefinition.name}}
                </span>
                <i class="glyphicon {{currentAppDefinition.definition.icon}}"></i>

                <div class="dropdown btn-group btn-group-sm" activiti-fix-dropdown-bug>
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                    	{{account.firstName && account.firstName != 'null' ? account.firstName : ''}} {{account.lastName && account.lastName != 'null' ? account.lastName : ''}}
                        <span class="glyphicon glyphicon-chevron-down" style="font-size: 10px" aria-hidden="true"></span>
                    </button>
                    <ul class="dropdown-menu pull-right">
                        <li><a href="" ng-click="logout()" translate>GENERAL.ACTION.LOGOUT</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!--[if lt IE 9]>
    <div class="unsupported-browser">
        <p class="alert error">You are using an unsupported browser. Please upgrade your browser in order to use the
            editor.</p>
    </div>
    <![endif]-->
    <!--[if IE 9]>
    <div id="no-flash-message" class="unsupported-browser" style="display: none">
        <p class="alert error">No Adobe Flash installed. Please install Adobe Flash in order to use the file upload capabilities on this version of Internet Explorer.</p>
    </div>
    <script>
        var hasFlash = false;
        try {
            hasFlash = Boolean(new ActiveXObject('ShockwaveFlash.ShockwaveFlash'));
        } catch(exception) {
            hasFlash = ('undefined' != typeof navigator.mimeTypes['application/x-shockwave-flash']);
        }
        if (!hasFlash) {
            var showNoFlashMessage = function () {
                var element = document.getElementById('no-flash-message');
                element.style.display="block";
            };
            showNoFlashMessage();
        }
    </script>
    <![endif]-->
    <div class="alert-wrapper" ng-cloak>
        <div class="alert fadein {{alerts.current.type}}" ng-show="alerts.current" ng-click="dismissAlert()">
            <i class="glyphicon"
               ng-class="{'glyphicon-ok': alerts.current.type == 'info', 'glyphicon-remove': alerts.current.type == 'error'}"></i>
            <span>{{alerts.current.message}}</span>

            <div class="pull-right" ng-show="alerts.queue.length > 0">
                <span class="badge">{{alerts.queue.length + 1}}</span>
            </div>
        </div>
    </div>

    <div id="main" class="wrapper full clearfix" ng-view="" ng-cloak ng-style="{height: window.height + 'px'}">
    </div>

<!--[if lt IE 9]>
<script src="libs/es5-shim-********/es5-shim.js"></script>
<script src="libs/json3_3.2.6/lib/json3.min.js"></script>
<![endif]-->

<script src="libs/jquery_1.11.0/jquery.min.js"></script>
<script src="libs/jquery-ui-1.10.3.custom.min.js"></script>

<script src="libs/angular_1.3.13/angular.min.js"></script>
<script src="libs/angular-animate_1.3.13/angular-animate.min.js"></script>
<script src="libs/bootstrap_3.1.1/js/bootstrap.min.js"></script>
<script src="libs/angular-resource_1.3.13/angular-resource.min.js"></script>
<script src="libs/angular-cookies_1.3.13/angular-cookies.min.js"></script>
<script src="libs/angular-sanitize_1.3.13/angular-sanitize.min.js"></script>
<script src="libs/angular-route_1.3.13/angular-route.min.js"></script>
<script src="libs/angular-translate_2.15.1/angular-translate.min.js"></script>
<script src="libs/angular-translate-storage-cookie/angular-translate-storage-cookie.js"></script>
<script src="libs/angular-translate-loader-static-files/angular-translate-loader-static-files.js"></script>
<script src="libs/angular-strap_2.1.6/angular-strap.min.js"></script>
<script src="libs/angular-strap_2.1.6/angular-strap.tpl.min.js"></script>
<script src="libs/momentjs_2.18.1/momentjs.min.js"></script>
<script src="libs/bootstrap-tour_0.9.1/bootstrap-tour.min.js"></script>
<script src="libs/ng-file-upload/ng-file-upload-shim.min.js"></script>
<script src="libs/ng-file-upload/ng-file-upload.min.js"></script>

<script src="editor-app/libs/ui-utils.min-0.2.1.js" type="text/javascript"></script>
<script src="libs/ui-grid_3.0.0/ui-grid.js" type="text/javascript"></script>
<script src="libs/angular-dragdrop_1.0.11/angular-dragdrop.min.js" type="text/javascript"></script>
<script src="editor-app/libs/mousetrap/1.6.0/mousetrap.min.js" type="text/javascript"></script>
<script src="editor-app/libs/jquery.autogrow-textarea.js" type="text/javascript"></script>

<script src="libs/handsontable_0.31.2/handsontable.full.min.js" type="text/javascript"></script>
<script src="libs/ng-handsontable_0.13/ngHandsontable.js" type="text/javascript"></script>

<script src="editor-app/libs/prototype-1.7.3.js" type="text/javascript"></script>
<script src="editor-app/libs/path_parser.js" type="text/javascript"></script>

<script src="libs/angular-spectrum-colorpicker_1.0.13/spectrum.js" type="text/javascript"></script>
<script src="libs/angular-spectrum-colorpicker_1.0.13/angular-spectrum-colorpicker.min.js" type="text/javascript"></script>
<script src="libs/angular-scroll_0.5.7/angular-scroll.min.js" type="text/javascript"></script>
<script src="libs/angular-drag-and-drop-lists_1.2.0/angular-drag-and-drop-lists.js" type="text/javascript"></script>
<script src="libs/html2canvas_0.4.1/html2canvas.js"></script>

<!-- Configuration -->
<script src="scripts/app-cfg.js?v=2"></script>

<!-- build:js scripts/scripts.js -->
<script src="editor-app/editor/i18n/translation_en_us.js" type="text/javascript"></script>
<script src="editor-app/editor/i18n/translation_signavio_en_us.js" type="text/javascript"></script>
<script src="editor-app/editor/oryx.debug.js" type="text/javascript"></script>

<script src="scripts/app.js"></script>
<script src="scripts/configuration/url-config.js"></script>
<script src="scripts/editor-directives.js"></script>
<script src="scripts/controllers/processes.js"></script>
<script src="scripts/controllers/process.js"></script>
<script src="scripts/controllers/casemodels.js"></script>
<script src="scripts/controllers/casemodel.js"></script>
<script src="scripts/controllers/forms.js"></script>
<script src="scripts/controllers/form.js"></script>
<script src="scripts/controllers/decision-tables.js"></script>
<script src="scripts/controllers/decision-table.js"></script>
<script src="scripts/controllers/app-definitions.js"></script>
<script src="scripts/controllers/app-definition.js"></script>
<script src="scripts/controllers/model-common-actions.js"></script>

<script src="scripts/services/util-services.js"></script>
<script src="scripts/services/identity-services.js"></script>

<script src="scripts/services/form-services.js"></script>
<script src="scripts/controllers/form-builder.js"></script>
<script src="scripts/configuration/form-builder-toolbar-default-actions.js" type="text/javascript"></script>
<script src="scripts/configuration/form-builder-toolbar.js" type="text/javascript"></script>
<script src="scripts/controllers/form-toolbar-controller.js" type="text/javascript"></script>
<script src="scripts/controllers/form-readonly-view.js"></script>

<script src="scripts/services/decision-table-service.js"></script>
<script src="scripts/controllers/decision-table-editor.js"></script>
<script src="scripts/configuration/decision-table-toolbar-default-actions.js" type="text/javascript"></script>
<script src="scripts/configuration/decision-table-toolbar.js" type="text/javascript"></script>
<script src="scripts/controllers/decision-table-toolbar-controller.js" type="text/javascript"></script>

<script src="scripts/controllers/app-definition-builder.js"></script>
<script src="scripts/configuration/app-definition-toolbar-default-actions.js" type="text/javascript"></script>
<script src="scripts/configuration/app-definition-toolbar.js" type="text/javascript"></script>
<script src="scripts/controllers/app-definition-toolbar-controller.js" type="text/javascript"></script>

<script src="editor-app/eventbus.js" type="text/javascript"></script>

<script src="editor-app/editor-controller.js" type="text/javascript"></script>
<script src="editor-app/stencil-controller.js" type="text/javascript"></script>
<script src="editor-app/toolbar-controller.js" type="text/javascript"></script>
<script src="editor-app/header-controller.js" type="text/javascript"></script>
<script src="editor-app/select-shape-controller.js" type="text/javascript"></script>
<script src="editor-app/define-data-controller.js" type="text/javascript"></script>
<script src="editor-app/process-navigator-controller.js" type="text/javascript"></script>
<script src="editor-app/editormanager.js" type="text/javascript"></script>

<script src="editor-app/tour.js" type="text/javascript"></script>
<script src="editor-app/editor-utils.js" type="text/javascript"></script>
<script src="editor-app/configuration/toolbar-default-actions.js" type="text/javascript"></script>

<script src="editor-app/configuration/properties-data-properties-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-default-controllers.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-execution-listeners-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-event-listeners-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-assignment-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-fields-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-form-properties-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-in-parameters-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-calledelementtype-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-multiinstance-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-process-historylevel-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-ordering-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-out-parameters-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-task-listeners-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-form-reference-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-sequenceflow-order-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-condition-expression-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-signal-definitions-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-signal-scope-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-message-definitions-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-message-scope-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-duedate-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-decisiontable-reference-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-case-reference-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-process-reference-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-transition-event-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-trigger-mode-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-planitem-dropdown-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-plan-item-lifecycle-listeners-controller.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-httprequest-controller.js" type="text/javascript"></script>

<script src="editor-app/editor-config.js" type="text/javascript"></script>

<script src="editor-app/configuration/url-config.js" type="text/javascript"></script>

<script src="editor-app/configuration/toolbar.js" type="text/javascript"></script>
<script src="editor-app/configuration/toolbar-custom-actions.js" type="text/javascript"></script>

<script src="editor-app/configuration/properties.js" type="text/javascript"></script>
<script src="editor-app/configuration/properties-custom-controllers.js" type="text/javascript"></script>

<script src="editor-app/configuration/flowable-header-custom.js" type="text/javascript"></script>
<script src="editor-app/configuration/flowable-toolbar-custom-actions.js" type="text/javascript"></script>

<script src="scripts/common/directives.js"></script>
<script src="scripts/common/providers-config.js"></script>
<script src="scripts/common/services/resource-service.js"></script>
<script src="scripts/common/services/recursion-helper.js"></script>
<script src="scripts/common/controllers/about.js"></script>
<!-- endbuild -->

<!-- Integration extensions -->
<script src="scripts/resource-loader.js?v=2" app="editor"></script>

</body>
</html>
