<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
<properties>
		<property name="prefix" value="" />
		<!-- MySQL使用如下类型 -->
		<property name="blobType" value="BLOB" />
		<!-- PostgreSQL使用如下类型 -->
		<!--<property name="blobType" value="VARCHAR" />-->
	</properties>

	<mappers>
		<!-- 对应Bean类的xml配置文件的路径信息 -->
		<mapper resource="META-INF/modeler-mybatis-mappings/Model.xml" />
		<mapper resource="META-INF/modeler-mybatis-mappings/ModelHistory.xml" />
		<mapper resource="META-INF/modeler-mybatis-mappings/ModelRelation.xml" />
	</mappers>

</configuration>
