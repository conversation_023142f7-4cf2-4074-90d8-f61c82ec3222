package com.snszyk.resource.oss;

import com.google.common.collect.Multimap;
import com.snszyk.core.oss.MinioTemplate;
import com.snszyk.core.oss.props.OssProperties;
import com.snszyk.core.oss.rule.OssRule;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.Part;
import lombok.SneakyThrows;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * SzykMinioTemplate 支持分片上传
 * <AUTHOR>
 */
public class SzykMinioTemplate extends MinioTemplate {

	//支持分片上传
	private final SzykMinioClient szykMinioClient;

	public SzykMinioTemplate(SzykMinioClient szykMinioClient, OssRule ossRule, OssProperties ossProperties) {
		super(szykMinioClient, ossRule, ossProperties);
		this.szykMinioClient = szykMinioClient;
	}

	/**
	 *  上传分片上传请求，返回uploadId
	 */
	public CreateMultipartUploadResponse uploadId(String bucketName, String region, String objectName,
												  Multimap<String, String> headers,
												  Multimap<String, String> extraQueryParams)
		throws NoSuchAlgorithmException, InsufficientDataException, IOException, InvalidKeyException, ServerException,
		XmlParserException, ErrorResponseException, InternalException, InvalidResponseException {
		return szykMinioClient.createMultipartUpload(bucketName, region, objectName, headers, extraQueryParams);
	}

	/**
	 * 完成分片上传，执行合并文件
	 *
	 * @param bucketName       存储桶
	 * @param region           区域
	 * @param objectName       对象名
	 * @param uploadId         上传ID
	 * @param parts            分片
	 * @param extraHeaders     额外消息头
	 * @param extraQueryParams 额外查询参数
	 */
	public ObjectWriteResponse completeMultipartUpload(String bucketName, String region, String objectName,
													   String uploadId, Part[] parts,
													   Multimap<String, String> extraHeaders,
													   Multimap<String, String> extraQueryParams)
		throws NoSuchAlgorithmException, InsufficientDataException, IOException, InvalidKeyException, ServerException,
		XmlParserException, ErrorResponseException, InternalException, InvalidResponseException {
		return szykMinioClient.completeMultipartUpload(bucketName, region, objectName, uploadId, parts,
			extraHeaders, extraQueryParams);
	}

	/**
	 * 查询分片数据
	 *
	 * @param bucketName       存储桶
	 * @param region           区域
	 * @param objectName       对象名
	 * @param uploadId         上传ID
	 * @param extraHeaders     额外消息头
	 * @param extraQueryParams 额外查询参数
	 */
	public ListPartsResponse listMultipart(String bucketName, String region, String objectName, Integer maxParts,
										   Integer partNumberMarker, String uploadId,
										   Multimap<String, String> extraHeaders,
										   Multimap<String, String> extraQueryParams) throws NoSuchAlgorithmException,
		InsufficientDataException, IOException, InvalidKeyException, ServerException, XmlParserException,
		ErrorResponseException, InternalException, InvalidResponseException {
		return szykMinioClient.listMultipart(bucketName, region, objectName, maxParts, partNumberMarker, uploadId,
			extraHeaders, extraQueryParams);
	}

	/**
	 * 返回临时带签名、过期时间七天、Get请求方式的访问URL
	 *
	 * @param bucketName  桶名
	 * @param ossFilePath Oss文件路径
	 * @param queryParams 查询参数
	 * @return
	 */
	@SneakyThrows
	public String getPresignedObjectUrl(String bucketName, String ossFilePath, Map<String, String> queryParams) {
		return szykMinioClient.getPresignedObjectUrl(
			GetPresignedObjectUrlArgs.builder()
				.method(Method.PUT)
				.bucket(bucketName)
				.object(ossFilePath)
				.expiry(60 * 60 * 24 * 7)
				.extraQueryParams(queryParams)
				.build());
	}

	/**
	 * 判断文件是否存在
	 *
	 * @param bucketName 桶名称
	 * @param objectName 文件名称
	 * @return true存在, 反之
	 */
	public Boolean checkFileIsExist(String bucketName, String objectName) {
		try {
			szykMinioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
}
