/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.builder.oss;

import io.minio.MinioClient;
import lombok.SneakyThrows;
import com.snszyk.core.oss.OssTemplate;
import com.snszyk.core.oss.MinioTemplate;
import com.snszyk.core.oss.props.OssProperties;
import com.snszyk.core.oss.rule.OssRule;
import com.snszyk.resource.entity.Oss;

/**
 * Minio云存储构建类
 *
 * <AUTHOR>
 */
public class MinioOssBuilder {

	@SneakyThrows
	public static OssTemplate template(Oss oss, OssRule ossRule) {
		MinioClient minioClient = MinioClient.builder()
			.endpoint(oss.getEndpoint())
			.credentials(oss.getAccessKey(), oss.getSecretKey())
			.build();
		OssProperties ossProperties = new OssProperties();
		ossProperties.setEndpoint(oss.getEndpoint());
		ossProperties.setAccessKey(oss.getAccessKey());
		ossProperties.setSecretKey(oss.getSecretKey());
		ossProperties.setBucketName(oss.getBucketName());
		return new MinioTemplate(minioClient, ossRule, ossProperties);
	}

}
