/*
 *      Copyright (c) 2018-2028
 */
package $!{package.Controller};

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.utils.ValidationUtils;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.RoleConstant;

import com.snszyk.demo.service.logic.OrderLogicService;
#set($servicePackage=$package.Entity.replace("entity","service"))
import com.snszyk.demo.vo.OrderVo;
#set($voPackage=$package.Entity.replace("entity","vo"))
import $!{voPackage}.$!{entity}Vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * $!{table.comment} 控制器
 *
 * <AUTHOR>
 * @since $!{date}
 */
@RestController
@AllArgsConstructor
@RequestMapping("$!{cfg.serviceName}/$!{cfg.entityKey}")
@Api(value = "$!{table.comment}", tags = "$!{table.comment}接口")
#if($!{superControllerClass})
public class $!{table.controllerName} extends $!{superControllerClass} {
#else
public class $!{table.controllerName} {
#end

	// private final $!{table.serviceName} $!{table.entityPath}Service;

    private final $!{entity}LogicService $!{table.entityPath}LogicService;

    @Override
    protected BaseCrudLogicService fetchBaseLogicService() {
        return $!{table.entityPath}LogicService;
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "保存", notes = "$!{entity}Vo")
    public R<BaseCrudDto> save(@RequestBody $!{entity}Vo v) {
        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
        return R.data(baseCrudDto);
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "$!{entity}Vo")
    public R<IPage<BaseCrudDto>> page($!{entity}Vo v) {
        IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
        return R.data(pageQueryResult);
    }

    /**
    * 列表
    */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "列表", notes = "$!{entity}Vo")
    public R<List<BaseCrudDto>> list($!{entity}Vo v) {
        List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
        return R.data(listQueryResult);
    }

    /**
     * 获取单条
     */
    @GetMapping("/fetchOne")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取单条数据", notes = "$!{entity}Vo")
    public R<BaseCrudDto> fetchOne($!{entity}Vo v) {
        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
        return R.data(baseCrudDto);
    }

    /**
     * 根据ID获取数据
     */
    @Override
    @GetMapping("/fetchById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据ID获取数据", notes = "id")
    public R<BaseCrudDto> fetchById(Long id) {
        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
        return R.data(baseCrudDto);
    }

    /**
     * 删除
     */
    @Override
    @PostMapping("/deleteById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "删除", notes = "id")
    public R<Boolean> deleteById(Long id) {
        Boolean result = this.fetchBaseLogicService().deleteById(id);
        return R.data(result);
    }

}
