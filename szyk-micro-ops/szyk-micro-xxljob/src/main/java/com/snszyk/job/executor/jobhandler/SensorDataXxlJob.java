package com.snszyk.job.executor.jobhandler;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.system.user.feign.IUserSearchClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 传感器数据异常提醒定时任务
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SensorDataXxlJob {

	private static final Logger logger = LoggerFactory.getLogger(SensorDataXxlJob.class);

	private final IMessageClient messageClient;
	private final IUserSearchClient userSearchClient;

	/**
	 * 传感器数据超时提醒 定时任务
	 * @param param 参数：{"timeout_in_minute":120,"receiver_role_ids":"123456,123457"}
	 *              timeout_in_minute: 超时时长（分钟），如果某个位号最新一条数据的create_time已超过此时长，则报警；
	 *              receiver_role_ids：超时报警消息接收者角色ids，多个用英文逗号（','）分隔。
	 * @return result
	 */
	@XxlJob("sensorDataTimeoutJobHandler")
	public ReturnT<String> sensorDataTimeoutJobHandler(String param) {
		logger.info("传感器数据超时提醒 - 定时任务 - 【开始】");

		//1、参数解析
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}
		JSONObject jsonObject = JSONObject.parseObject(param);
		logger.info("任务参数为：{}", jsonObject);
		int timeoutInMinute = jsonObject.getIntValue("timeout_in_minute");
		String receiverRoleIds = jsonObject.getString("receiver_role_ids");
		Date timeoutTime = DateUtil.minusMinutes(new Date(), timeoutInMinute);

		//2、查询所有位号（带测点路径 和 传感器参数名称）的最新一条数据的时间
		//List<DaqConfigDTO> daqConfigList = daqConfigService.listLatestDataByIotCode();

		//3、检查是否有超时的数据
		//if (CollectionUtil.isNotEmpty(daqConfigList)) {
		//	//已超时的位号
		//	List<DaqConfigDTO> timeoutDaqConfigList = daqConfigList.stream()
		//		.filter(daqConfigDTO -> daqConfigDTO.getMaxCreateTime() != null
		//			&& daqConfigDTO.getMaxCreateTime().before(timeoutTime))
		//		.collect(Collectors.toList());
		//
		//	if (CollectionUtil.isNotEmpty(timeoutDaqConfigList)) {
		//		R<List<User>> userResult = userSearchClient.listByRole(receiverRoleIds);
		//		if (userResult.isSuccess() && CollectionUtil.isNotEmpty(userResult.getData())) {
		//			//发送采集站离线待办消息
		//			MessageVo messageVo = new MessageVo();
		//			messageVo.setAppKey("SiDAs");
		//			messageVo.setTitle("传感器数据超时通知");
		//			//content改为json字符串：测点路径 + 传感器编码
		//			List<String> msgContent = timeoutDaqConfigList.stream()
		//				.sorted(Comparator.comparing(DaqConfigDTO::getPathName, Comparator.naturalOrder()))
		//				.map(daqConfigDTO -> daqConfigDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH) +
		//					" - " + daqConfigDTO.getSensorCode() +
		//					" - " + DateUtil.format(daqConfigDTO.getMaxCreateTime(), "yyyy-MM-dd HH:mm:ss"))
		//				.distinct()
		//				.collect(Collectors.toList());
		//			logger.info("有{}个传感器的数据超时！！！", msgContent.size());
		//			messageVo.setContent(JSONArray.toJSONString(msgContent));
		//			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		//			messageVo.setBizType(MessageBizTypeEnum.SENSOR_DATA_TIMEOUT.getCode());
		//			messageVo.setBizId("12345678");
		//			messageVo.setSender("SiDAs");
		//			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		//			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		//			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		//			List<ReceiverInfoVo.UserVo> userVoList = userResult.getData().stream().map(user -> {
		//				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
		//				userVo.setId(user.getId());
		//				userVo.setRealName(user.getRealName());
		//				return userVo;
		//			}).collect(Collectors.toList());
		//			receiverInfoVo.setUserList(userVoList);
		//			messageVo.setReceiverInfoVo(receiverInfoVo);
		//			logger.info("发送传感器数据超时消息 - 接收人：{}", JSONObject.toJSONString(userVoList));
		//			R messageResult = messageClient.pushMessage(messageVo);
		//			logger.info("发送传感器数据超时消息 - 结果：{}", JSONObject.toJSONString(messageResult));
		//		} else {
		//			logger.warn("获取用户列表失败！code = {}, msg = {}.", userResult.getCode(), userResult.getMsg());
		//		}
		//	} else {
		//		logger.info("暂无传感器数据超时 - 一切正常~~~");
		//	}
		//} else {
		//	logger.info("暂无传感器数据 - 等待传感器上传数据！");
		//}

		logger.info("传感器数据超时提醒 - 定时任务 - 【结束】");
		return ReturnT.SUCCESS;
	}

	/**
	 * 传感器低电量提醒 定时任务
	 * @param param 参数：{"min_battery":2.8,"receiver_role_ids":"123456,123457"}
	 * 	                min_battery: 最低电量值，如果电量的值低于此值（根据不同厂家的传感器做响应调整），则报警
	 * 	                receiver_role_ids：超时报警消息接收者角色ids，多个用英文逗号（','）分隔。
	 * @return result
	 */
	@XxlJob("lowBatteryJobHandler")
	public ReturnT<String> lowBatteryJobHandler(String param) {
		logger.info("传感器低电量提醒 - 定时任务 - 【开始】");

		//1、参数解析
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}
		JSONObject jsonObject = JSONObject.parseObject(param);
		logger.info("任务参数为：{}", jsonObject);
		double minBattery = jsonObject.getDoubleValue("min_battery");
		String receiverRoleIds = jsonObject.getString("receiver_role_ids");

		//2、查询所有电量位号（带测点路径 和 传感器参数名称）的最新一条数据的值
		//List<DaqConfigDTO> daqConfigList = daqConfigService.listLatestPowerDataByIotCode();
		//
		////3、检查是否有超时的数据
		//if (CollectionUtil.isNotEmpty(daqConfigList)) {
		//	//低电量的位号
		//	List<DaqConfigDTO> timeoutDaqConfigList = daqConfigList.stream()
		//		.filter(daqConfigDTO -> daqConfigDTO.getCharacteristicValue().compareTo(BigDecimal.valueOf(minBattery)) <= 0)
		//		.collect(Collectors.toList());
		//
		//	if (CollectionUtil.isNotEmpty(timeoutDaqConfigList)) {
		//		R<List<User>> userResult = userSearchClient.listByRole(receiverRoleIds);
		//		if (userResult.isSuccess() && CollectionUtil.isNotEmpty(userResult.getData())) {
		//			//发送采集站离线待办消息
		//			MessageVo messageVo = new MessageVo();
		//			messageVo.setAppKey("SiDAs");
		//			messageVo.setTitle("传感器低电量通知");
		//			//content改为json字符串：测点路径 + 传感器编码
		//			//List<String> msgContent = timeoutDaqConfigList.stream()
		//			//	.sorted(Comparator.comparing(DaqConfigDTO::getPathName, Comparator.naturalOrder()))
		//			//	.map(daqConfigDTO -> daqConfigDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH) +
		//			//		" - " + daqConfigDTO.getSensorCode() +
		//			//		" - " + daqConfigDTO.getCharacteristicValue().setScale(2, RoundingMode.HALF_UP))
		//			//	.distinct()
		//			//	.collect(Collectors.toList());
		//			List<String> msgContent = new ArrayList<>();
		//			logger.info("有{}个低电量的传感器！！！", msgContent.size());
		//			messageVo.setContent(JSONArray.toJSONString(msgContent));
		//			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		//			messageVo.setBizType(MessageBizTypeEnum.SENSOR_LOW_BATTERY.getCode());
		//			messageVo.setBizId("12341234");
		//			messageVo.setSender("SiDAs");
		//			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		//			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		//			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		//			List<ReceiverInfoVo.UserVo> userVoList = userResult.getData().stream().map(user -> {
		//				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
		//				userVo.setId(user.getId());
		//				userVo.setRealName(user.getRealName());
		//				return userVo;
		//			}).collect(Collectors.toList());
		//			receiverInfoVo.setUserList(userVoList);
		//			messageVo.setReceiverInfoVo(receiverInfoVo);
		//			logger.info("发送传感器低电量消息 - 接收人：{}", JSONObject.toJSONString(userVoList));
		//			R messageResult = messageClient.pushMessage(messageVo);
		//			logger.info("发送传感器低电量消息 - 结果：{}", JSONObject.toJSONString(messageResult));
		//		} else {
		//			logger.warn("获取用户列表失败！code = {}, msg = {}.", userResult.getCode(), userResult.getMsg());
		//		}
		//	} else {
		//		logger.info("暂无低电量传感器 - 一切正常~~~");
		//	}
		//} else {
		//	logger.info("暂无低电量传感器数据 - 等待传感器上传数据！");
		//}

		logger.info("传感器低电量提醒 - 定时任务 - 【结束】");
		return ReturnT.SUCCESS;
	}

}
