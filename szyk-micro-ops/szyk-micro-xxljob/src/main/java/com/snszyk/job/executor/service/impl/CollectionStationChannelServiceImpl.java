/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.job.executor.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.job.executor.mapper.CollectionStationChannelMapper;
import com.snszyk.job.executor.service.ICollectionStationChannelService;
import com.snszyk.sidas.basic.dto.CollectionStationChannelDTO;
import com.snszyk.sidas.basic.entity.CollectionStationChannel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采集站通道表 服务实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class CollectionStationChannelServiceImpl extends ServiceImpl<CollectionStationChannelMapper, CollectionStationChannel>
	implements ICollectionStationChannelService {

	@Override
	public List<CollectionStationChannelDTO> selectChannelList(Long stationId) {
		return baseMapper.selectChannelList(stationId);
	}

}
