package com.snszyk.job.executor.jobhandler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.sidas.basic.entity.PointValue;
import com.snszyk.sidas.basic.enums.VibrationTypeEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * XxlJob开发示例（Bean模式）
 * <p>
 * 开发步骤：
 * 1、在Spring Bean实例中，开发Job方法，方式格式要求为 "public ReturnT<String> execute(String param)"
 * 2、为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 * 3、执行日志：需要通过 "XxlJobLogger.log" 打印执行日志；
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SampleXxlJob {
	private static final Logger logger = LoggerFactory.getLogger(SampleXxlJob.class);

	private final RabbitTemplate rabbitTemplate;

	/**
	 * 模拟iot-dc3中生成传感器原始数据 - 注：振动类型数据的最大采样频率为64KHz！！！
	 *
	 * @param param 输入参数json - [{"vibrationType":0,"rangeMax":100.2,"samplingPoints":0,"iotCode":"123"},
	 *              			  {"vibrationType":0,"rangeList":["0","1","2","3"],"samplingPoints":0,"iotCode":"123"},
	 *                           {"vibrationType":1,"rangeMax":156.0,"samplingPoints":1024,"iotCode":"1234"}]
	 * @return
	 * @throws Exception
	 */
	@XxlJob("iotSensorRawData")
	public ReturnT<String> iotSensorRawData(String param) throws Exception {
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}

		JSONArray paramArray = JSONArray.parseArray(param);
		logger.info("任务参数为：{}", paramArray);
		for (int i = 0; i < paramArray.size(); i++) {
			JSONObject jsonObject = paramArray.getJSONObject(i);
			PointValue pointValue = new PointValue();
			pointValue.setId("123456");
			pointValue.setDeviceId("123456");
			pointValue.setPointId("123456");
			pointValue.setOriginTime(DateUtil.now());
			pointValue.setDevicePointBindCode(jsonObject.getString("iotCode"));
			Integer vibrationType = jsonObject.getInteger("vibrationType");
			double rangeMax = jsonObject.getDoubleValue("rangeMax");
			JSONArray rangeList = jsonObject.getJSONArray("rangeList");
			if (VibrationTypeEnum.NON_VIBRATION.getCode().equals(vibrationType)) {
				//非振动数据
				if (rangeList != null && rangeList.size() > 0) {
					pointValue.setValue(rangeList.getString(new Random().nextInt(rangeList.size())));
				} else {
					pointValue.setValue(Math.random() * rangeMax + "");
				}
			} else {
				//振动数据
				pointValue.setValue(generateValue(jsonObject.getIntValue("samplingPoints")));
			}
			pointValue.setCreateTime(DateUtil.now());

			//发送传感器数据到设备诊断
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.FANOUT_EXCHANGE_VALUE, "", pointValue);
		}

		return ReturnT.SUCCESS;
	}

	/**
	 * 根据真实震动数据生成
	 * @param samplingPoints 采样点数
	 * @return
	 */
	private static String generateValue(int samplingPoints) {
		//校验采样频率范围
		if (samplingPoints > (8 * 1024) || samplingPoints <= 0) {
			logger.warn("采样点数({})超过了8K，或小于0！", samplingPoints);
			return "[0]";
		}

		//随机选择数据文件
		String rawDataFilePath;
		switch ((int) (System.currentTimeMillis() % 6)) {
			case 0:
				rawDataFilePath = "raw-data/data_1.txt";
				break;
			case 1:
				rawDataFilePath = "raw-data/data_2.txt";
				break;
			case 2:
				rawDataFilePath = "raw-data/data_3.txt";
				break;
			case 3:
				rawDataFilePath = "raw-data/data_4.txt";
				break;
			case 4:
				rawDataFilePath = "raw-data/data_5.txt";
				break;
			case 5:
			default:
				rawDataFilePath = "raw-data/data_6.txt";
				break;
		}

		logger.info("selected raw data file path = {}", rawDataFilePath);
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ClassPathResource(rawDataFilePath).getInputStream()))) {
			double[] value = new double[samplingPoints];

			String line;
			int count = 0;
			while (StrUtil.isNotEmpty(line = reader.readLine())) {
				//读取数据
				value[count++] = Double.parseDouble(line.trim());

				//读取完毕
				if (count >= samplingPoints) {
					break;
				}
			}

			return Arrays.toString(value);
		} catch (Exception e) {
			e.printStackTrace();
			return "[0]";
		}
	}

	/**
	 * 1、简单任务示例（Bean模式）
	 */
	@XxlJob("demoJobHandler")
	public ReturnT<String> demoJobHandler(String param) throws Exception {
		XxlJobLogger.log("XXL-JOB, Hello World.");
		logger.info("XXL-JOB, Hello World.");

		for (int i = 0; i < 3; i++) {
			XxlJobLogger.log("beat at:" + i);
			logger.info("beat at: " + i);
			TimeUnit.SECONDS.sleep(1);
		}

		return ReturnT.SUCCESS;
	}


	/**
	 * 2、分片广播任务
	 */
	@XxlJob("shardingJobHandler")
	public ReturnT<String> shardingJobHandler(String param) throws Exception {

		// 分片参数
		ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
		XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());

		// 业务逻辑
		for (int i = 0; i < shardingVO.getTotal(); i++) {
			if (i == shardingVO.getIndex()) {
				XxlJobLogger.log("第 {} 片, 命中分片开始处理", i);
			} else {
				XxlJobLogger.log("第 {} 片, 忽略", i);
			}
		}

		return ReturnT.SUCCESS;
	}


	/**
	 * 3、命令行任务
	 */
	@XxlJob("commandJobHandler")
	public ReturnT<String> commandJobHandler(String param) throws Exception {
		String command = param;
		int exitValue = -1;

		BufferedReader bufferedReader = null;
		try {
			// command process
			Process process = Runtime.getRuntime().exec(command);
			BufferedInputStream bufferedInputStream = new BufferedInputStream(process.getInputStream());
			bufferedReader = new BufferedReader(new InputStreamReader(bufferedInputStream));

			// command log
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				XxlJobLogger.log(line);
			}

			// command exit
			process.waitFor();
			exitValue = process.exitValue();
		} catch (Exception e) {
			XxlJobLogger.log(e);
		} finally {
			if (bufferedReader != null) {
				bufferedReader.close();
			}
		}

		if (exitValue == 0) {
			return IJobHandler.SUCCESS;
		} else {
			return new ReturnT<String>(IJobHandler.FAIL.getCode(), "command exit value(" + exitValue + ") is failed");
		}
	}


	/**
	 * 4、跨平台Http任务
	 */
	@XxlJob("httpJobHandler")
	public ReturnT<String> httpJobHandler(String param) throws Exception {

		// request
		HttpURLConnection connection = null;
		BufferedReader bufferedReader = null;
		try {
			// connection
			URL realUrl = new URL(param);
			connection = (HttpURLConnection) realUrl.openConnection();

			// connection setting
			connection.setRequestMethod("GET");
			connection.setDoOutput(true);
			connection.setDoInput(true);
			connection.setUseCaches(false);
			connection.setReadTimeout(5 * 1000);
			connection.setConnectTimeout(3 * 1000);
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			connection.setRequestProperty("Accept-Charset", "application/json;charset=UTF-8");

			// do connection
			connection.connect();

			//Map<String, List<String>> map = connection.getHeaderFields();

			// valid StatusCode
			int statusCode = connection.getResponseCode();
			if (statusCode != 200) {
				throw new RuntimeException("Http Request StatusCode(" + statusCode + ") Invalid.");
			}

			// result
			bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			StringBuilder result = new StringBuilder();
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				result.append(line);
			}
			String responseMsg = result.toString();

			XxlJobLogger.log(responseMsg);
			return ReturnT.SUCCESS;
		} catch (Exception e) {
			XxlJobLogger.log(e);
			return ReturnT.FAIL;
		} finally {
			try {
				if (bufferedReader != null) {
					bufferedReader.close();
				}
				if (connection != null) {
					connection.disconnect();
				}
			} catch (Exception e2) {
				XxlJobLogger.log(e2);
			}
		}

	}

	/**
	 * 5、生命周期任务示例：任务初始化与销毁时，支持自定义相关逻辑；
	 */
	@XxlJob(value = "demoJobHandler2", init = "init", destroy = "destroy")
	public ReturnT<String> demoJobHandler2(String param) throws Exception {
		XxlJobLogger.log("XXL-JOB, Hello World.");
		return ReturnT.SUCCESS;
	}

	public void init() {
		logger.info("init");
	}

	public void destroy() {
		logger.info("destory");
	}

	/*public static void main(String[] args) {
		double[] data = {-14.29909, -5.90656};
		for (double datum : data) {
			System.out.println(datum);
		}
	}*/

}
