package com.snszyk.job.executor.jobhandler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.job.executor.service.IEquipmentSpotCheckConfigService;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckConfigDTO;
import com.snszyk.sidas.basic.enums.SpotCheckTypeEnum;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.feign.IUserSearchClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点检相关定时任务
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SpotCheckXxlJob {

	private static final Logger logger = LoggerFactory.getLogger(SpotCheckXxlJob.class);

	private final IEquipmentSpotCheckConfigService spotCheckConfigService;
	private final IMessageClient messageClient;
	private final IUserSearchClient userSearchClient;

	/**
	 * 点检计划提醒 任务（必须每小时执行一次 - 0 0 0/1 * * ?）
	 * @param param 参数：{"receiver_role_ids":"123456,123457"}
	 * @return
	 */
	@XxlJob("spotCheckRemindJobHandler")
	public ReturnT<String> spotCheckRemindJobHandler(String param) {
		logger.info("点检计划提醒 - 定时任务 - 【开始】");

		//1、参数解析
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}
		JSONObject jsonObject = JSONObject.parseObject(param);
		logger.info("任务参数为：{}", jsonObject);
		String receiverRoleIds = jsonObject.getString("receiver_role_ids");

		// 查询所有的设备点检计划
		List<EquipmentSpotCheckConfigDTO> spotCheckConfigList = spotCheckConfigService.listAllConfigs();
		List<EquipmentSpotCheckConfigDTO> needSpotCheckConfigList = spotCheckConfigList.stream().filter(config -> {
			//提醒时间与当前时间相等的点检计划
			Calendar spotCheckDate = Calendar.getInstance();
			if (SpotCheckTypeEnum.MONTH.getCode().equals(config.getType())) {
				// 月检
				spotCheckDate.set(Calendar.DAY_OF_MONTH, config.getDayOfMonth());
				spotCheckDate.set(Calendar.HOUR_OF_DAY, config.getHourOfDay());
			} else if (SpotCheckTypeEnum.WEEK.getCode().equals(config.getType())) {
				// 周检（DAY_OF_WEEK：1-周日，2-周一，3-周二，4-周三，5-周四，6-周五，7-周六）
				spotCheckDate.set(Calendar.DAY_OF_WEEK, (config.getDayOfWeek() + 1) > 7 ? 1 : (config.getDayOfWeek() + 1));
				spotCheckDate.set(Calendar.HOUR_OF_DAY, config.getHourOfDay());
			} else if (SpotCheckTypeEnum.DAY.getCode().equals(config.getType())) {
				// 日检
				spotCheckDate.set(Calendar.HOUR_OF_DAY, config.getHourOfDay());
			} else {
				return false;
			}

			//设置下次点检时间
			config.setSpotCheckTime(spotCheckDate.getTime());

			//提前N小时提醒
			spotCheckDate.add(Calendar.HOUR, -config.getRemindHourInAdvance());

			return DateUtil.format(DateUtil.now(), "yyyy-MM-dd HH").equals(DateUtil.format(spotCheckDate.getTime(), "yyyy-MM-dd HH"));
		}).collect(Collectors.toList());

		// 发送提醒消息
		if (CollectionUtil.isNotEmpty(needSpotCheckConfigList)) {
			R<List<User>> userResult = userSearchClient.listByRole(receiverRoleIds);
			if (userResult.isSuccess() && CollectionUtil.isNotEmpty(userResult.getData())) {
				//发送采集站离线待办消息
				MessageVo messageVo = new MessageVo();
				messageVo.setAppKey("SiDAs");
				messageVo.setTitle("设备点检提醒");
				//content改为json字符串：设备路径 + 点检日期
				List<String> msgContent = needSpotCheckConfigList.stream()
					.map(config -> config.getEquipmentPathName() + " - 下次点检时间："
						+ DateUtil.format(config.getSpotCheckTime(), "yyyy-MM-dd HH:00"))
					.collect(Collectors.toList());
				logger.info("有{}个设备需要点检！！！", msgContent.size());
				messageVo.setContent(JSONArray.toJSONString(msgContent));
				messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
				messageVo.setBizType(MessageBizTypeEnum.EQUIPMENT_SPOT_CHECK.getCode());
				messageVo.setBizId("123456789");
				messageVo.setSender("SiDAs");
				messageVo.setIsImmediate(YesNoEnum.YES.getCode());
				messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
				ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
				List<ReceiverInfoVo.UserVo> userVoList = userResult.getData().stream().map(user -> {
					ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
					userVo.setId(user.getId());
					userVo.setRealName(user.getRealName());
					return userVo;
				}).collect(Collectors.toList());
				receiverInfoVo.setUserList(userVoList);
				messageVo.setReceiverInfoVo(receiverInfoVo);
				logger.info("发送设备点检提醒 - 接收人：{}", JSONObject.toJSONString(userVoList));
				R messageResult = messageClient.pushMessage(messageVo);
				logger.info("发送设备点检提醒 - 结果：{}", JSONObject.toJSONString(messageResult));
			} else {
				logger.warn("获取用户列表失败！code = {}, msg = {}.", userResult.getCode(), userResult.getMsg());
			}
		} else {
			logger.info("暂无需要点检提醒的设备！");
		}

		logger.info("点检计划提醒 - 定时任务 - 【结束】");
		return ReturnT.SUCCESS;
	}

}
