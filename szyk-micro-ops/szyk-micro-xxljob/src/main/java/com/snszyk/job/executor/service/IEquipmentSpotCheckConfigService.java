package com.snszyk.job.executor.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckConfigDTO;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckConfig;

import java.util.List;

/**
 * 设备点检计划配置 service
 * <AUTHOR>
 */
public interface IEquipmentSpotCheckConfigService extends BaseService<EquipmentSpotCheckConfig> {

	List<EquipmentSpotCheckConfigDTO> listAllConfigs();
}
