knife4j:
  cloud:
    routes:
      - name: 授权模块
        uri: service-auth.sidas-test
        location: /szyk-auth/v2/api-docs
      - name: 工作台模块
        uri: service-desk.sidas-test
        location: /szyk-desk/v2/api-docs
      - name: 用户模块
        uri: service-user.sidas-test
        location: /szyk-user/v2/api-docs
      - name: 系统模块
        uri: service-system.sidas-test
        location: /szyk-system/v2/api-docs
      - name: 资源模块
        uri: service-resource.sidas-test
        location: /szyk-resource/v2/api-docs
      - name: 设备基础模块
        uri: sidas-basic.sidas-test
        location: /szyk-sidas-basic/v2/api-docs
      - name: 故障管理模块
        uri: sidas-fault.sidas-test
        location: /szyk-sidas-fault/v2/api-docs
      - name: 设备诊断模块
        uri: sidas-diagnosis.sidas-test
        location: /szyk-sidas-diagnosis/v2/api-docs
      - name: 消息中心模块
        uri: service-message.sidas-test
        location: /szyk-message/v2/api-docs
