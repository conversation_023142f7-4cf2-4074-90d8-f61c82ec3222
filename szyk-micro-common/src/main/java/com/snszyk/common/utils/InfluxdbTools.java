package com.snszyk.common.utils;

import com.influxdb.client.InfluxDBClient;
import com.snszyk.common.config.InfluxdbProperties;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public class InfluxdbTools extends AbstractInfluxdbTool{


	public  InfluxdbTools(InfluxDBClient influxDBClient,InfluxdbProperties influxdbProperties){
		super(influxDBClient,influxdbProperties);
	}

	@Override
	protected String[] configTagColumn() {
		return tagColumn;
	}

	@Override
	protected String[] configFieldColumn() {
		return fields;
	}

	private final static String[] tagColumn = {"sensorCode",
		"sampleDataType",
		"waveId",
		"invalid",
		"monitorId"};
	private final static String[] fields = {"axisDirection",
		"value",
		"hasWaveData",
		"measureDirection",
		"samplingFreq",
		"samplingPoints",
		"rmsValue",
		"peakValue",
		"alarmLevel",
		"isMarked",
		"peakPeakValue",
		"clearanceValue",
		"skewnessValue",
		"kurtosisValue",
		"invalidReason",
		"energyValue",
		"sensorInstance",
		"waveformUrl"};


}



