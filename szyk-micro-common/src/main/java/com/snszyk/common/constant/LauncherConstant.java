/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.common.constant;

import com.snszyk.core.launch.constant.AppConstant;

import static com.snszyk.core.launch.constant.AppConstant.APPLICATION_NAME_PREFIX;

/**
 * 启动常量
 *
 * <AUTHOR>
 */
public interface LauncherConstant {

	/**
	 * xxljob
	 */
	String APPLICATION_XXLJOB_NAME = APPLICATION_NAME_PREFIX + "xxljob";

	/**
	 * xxljob
	 */
	String APPLICATION_XXLJOB_ADMIN_NAME = APPLICATION_NAME_PREFIX + "xxljob-admin";

	/**
	 * nacos namespace
	 */
	String NACOS_NAMESPACE = "szyk_sidas";

	/**
	 * nacos group
	 */
	String NACOS_GROUP = "SIDAS_GROUP";

	/**
	 * nacos username
	 */
	String NACOS_USERNAME = "nacos";

	/**
	 * 12 / 生产 nacos
	 */
	String NACOS_PASSWORD = "B45dolB##lt&7fKt";

	/**
	 * nacos dev 地址
	 */
	String NACOS_DEV_ADDR = "**************:8848";

	/**
	 * nacos prod 地址
	 */
	String NACOS_PROD_ADDR = "**************:8848";

	/**
	 * nacos test 地址
	 */
	String NACOS_TEST_ADDR = "**************:8848";

	/**
	 * sentinel dev 地址
	 */
	String SENTINEL_DEV_ADDR = "sentinel-v1.rdframework:8719";

	/**
	 * sentinel prod 地址
	 */
	String SENTINEL_PROD_ADDR = "***********:8858";

	/**
	 * sentinel test 地址
	 */
	String SENTINEL_TEST_ADDR = "sentinel-v1.rdframework.************.nip.io:32393";

	/**
	 * seata dev 地址
	 */
	String SEATA_DEV_ADDR = "*************:8091";

	/**
	 * seata prod 地址
	 */
	String SEATA_PROD_ADDR = "***********:8091";

	/**
	 * seata test 地址
	 */
	String SEATA_TEST_ADDR = "***********:8091";

	/**
	 * zipkin dev 地址
	 */
	String ZIPKIN_DEV_ADDR = "zipkin-v1.rdframework:9411";

	/**
	 * zipkin prod 地址
	 */
	String ZIPKIN_PROD_ADDR = "http://***********:9411";

	/**
	 * zipkin test 地址
	 */
	String ZIPKIN_TEST_ADDR = "zipkin-v1.rdframework.************.nip.io:32393";

	/**
	 * elk dev 地址
	 */
	String ELK_DEV_ADDR = "*************:9000";

	/**
	 * elk prod 地址
	 */
	String ELK_PROD_ADDR = "***********:9000";

	/**
	 * elk test 地址
	 */
	String ELK_TEST_ADDR = "***********:9000";

	/**
	 * seata file模式
	 */
	String FILE_MODE = "file";

	/**
	 * seata nacos模式
	 */
	String NACOS_MODE = "nacos";

	/**
	 * seata default模式
	 */
	String DEFAULT_MODE = "default";

	/**
	 * seata group后缀
	 */
	String GROUP_NAME = "-group";

	/**
	 * seata 服务组格式
	 *
	 * @param appName 服务名
	 * @return group
	 */
	static String seataServiceGroup(String appName) {
		return appName.concat(GROUP_NAME);
	}

	/**
	 * 动态获取nacos地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_ADDR;
			default:
				return NACOS_DEV_ADDR;
		}
	}

	/**
	 * 动态获取sentinel地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String sentinelAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SENTINEL_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SENTINEL_TEST_ADDR;
			default:
				return SENTINEL_DEV_ADDR;
		}
	}

	/**
	 * 动态获取seata地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String seataAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SEATA_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SEATA_TEST_ADDR;
			default:
				return SEATA_DEV_ADDR;
		}
	}

	/**
	 * 动态获取zipkin地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String zipkinAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return ZIPKIN_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return ZIPKIN_TEST_ADDR;
			default:
				return ZIPKIN_DEV_ADDR;
		}
	}

	/**
	 * 动态获取elk地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String elkAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return ELK_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return ELK_TEST_ADDR;
			default:
				return ELK_DEV_ADDR;
		}
	}

}
