/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.common.launch;

import com.snszyk.common.constant.LauncherConstant;
import com.snszyk.core.auto.service.AutoService;
import com.snszyk.core.launch.constant.NacosConstant;
import com.snszyk.core.launch.service.LauncherService;
import com.snszyk.core.launch.utils.PropsUtil;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 *
 * <AUTHOR>
 */
@AutoService(LauncherService.class)
public class LauncherServiceImpl implements LauncherService {

	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
		Properties props = System.getProperties();
		// 通用注册
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.server-addr", LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", LauncherConstant.NACOS_NAMESPACE);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.username", LauncherConstant.NACOS_USERNAME);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.password", LauncherConstant.NACOS_PASSWORD);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", LauncherConstant.NACOS_NAMESPACE);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.username", LauncherConstant.NACOS_USERNAME);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.password", LauncherConstant.NACOS_PASSWORD);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr", LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", LauncherConstant.NACOS_NAMESPACE);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", LauncherConstant.NACOS_GROUP);
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[0].data-id", NacosConstant.sharedDataId());
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[0].group", LauncherConstant.NACOS_GROUP);
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[0].namespace", LauncherConstant.NACOS_NAMESPACE);
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[1].data-id", NacosConstant.sharedDataId(profile));
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[1].group", LauncherConstant.NACOS_GROUP);
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.shared-configs[1].namespace", LauncherConstant.NACOS_NAMESPACE);
		PropsUtil.setProperty(props, "spring.cloud.sentinel.transport.dashboard", LauncherConstant.sentinelAddr(profile));
		PropsUtil.setProperty(props, "spring.zipkin.base-url", LauncherConstant.zipkinAddr(profile));
		PropsUtil.setProperty(props, "spring.datasource.dynamic.enabled", "false");

		// 开启elk日志
		// PropsUtil.setProperty(props, "szyk.log.elk.destination",
		// LauncherConstant.elkAddr(profile));

		// seata注册地址
		// PropsUtil.setProperty(props, "seata.service.grouplist.default",
		// LauncherConstant.seataAddr(profile));
		// seata注册group格式
		// PropsUtil.setProperty(props, "seata.tx-service-group",
		// LauncherConstant.seataServiceGroup(appName));
		// seata配置服务group
		// PropsUtil.setProperty(props,
		// "seata.service.vgroup-mapping.".concat(LauncherConstant.seataServiceGroup(appName)),
		// LauncherConstant.DEFAULT_MODE);
		// seata注册模式配置
		// PropsUtil.setProperty(props, "seata.registry.type",
		// LauncherConstant.NACOS_MODE);
		// PropsUtil.setProperty(props, "seata.registry.nacos.server-addr",
		// LauncherConstant.nacosAddr(profile));
		// PropsUtil.setProperty(props, "seata.config.type",
		// LauncherConstant.NACOS_MODE);
		// PropsUtil.setProperty(props, "seata.config.nacos.server-addr",
		// LauncherConstant.nacosAddr(profile));
	}

}
