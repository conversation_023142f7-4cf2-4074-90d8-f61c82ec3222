/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.mapper.UserMapper;
import com.snszyk.system.user.service.IUserSearchService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserSearchServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserSearchService {

	@Override
	public List<User> listAllUser(String tenantId) {
		return this.list(Wrappers.<User>lambdaQuery()
			.eq(StringUtil.isNotBlank(tenantId), TenantEntity::getTenantId, tenantId)
			.eq(BaseEntity::getIsDeleted, 0));
	}

	@Override
	public List<User> listByUser(List<Long> userId) {
		return this.list(Wrappers.<User>lambdaQuery().in(User::getId, userId));
	}

	@Override
	public List<User> listByDept(List<Long> deptId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		deptId.forEach(id -> queryWrapper.like(User::getDeptId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByPost(List<Long> postId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		postId.forEach(id -> queryWrapper.like(User::getPostId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByRole(List<Long> roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		roleId.forEach(id -> queryWrapper.like(User::getRoleId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByDeptRole(List<Long> deptId, List<Long> roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		deptId.forEach(id -> queryWrapper.like(User::getDeptId, id).or());
		queryWrapper.and(wrapper -> roleId.forEach(id -> wrapper.like(User::getRoleId, id).or()));
		return this.list(queryWrapper);
	}

}
