/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.desk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.desk.entity.Notice;
import com.snszyk.desk.vo.NoticeVO;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface INoticeService extends BaseService<Notice> {

	/**
	 * 自定义分页
	 * @param page
	 * @param notice
	 * @return
	 */
	IPage<NoticeVO> selectNoticePage(IPage<NoticeVO> page, NoticeVO notice);

}
