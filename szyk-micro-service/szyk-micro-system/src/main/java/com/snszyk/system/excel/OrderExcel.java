package com.snszyk.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.system.excel.converter.OrderPaymentTypeConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出Excel实体类
 * <AUTHOR>
 */
@Data
@ColumnWidth(30)
@HeadRowHeight(20)
@ContentRowHeight(18)
@EqualsAndHashCode
public class OrderExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单号
	 */
	@ExcelProperty("订单号")
	private String num;

	/**
	 * 订单日期
	 */
	@ExcelProperty("订单日期")
	private Date orderTime;

	/**
	 * 价格
	 */
	@ExcelProperty("价格")
	private BigDecimal price;

	/**
	 * 支付方式：1-银行卡；2-支付宝；3-微信；4-其他
	 */
	@ExcelProperty(value = "支付方式", converter = OrderPaymentTypeConverter.class)
	private Integer paymentType;

}
