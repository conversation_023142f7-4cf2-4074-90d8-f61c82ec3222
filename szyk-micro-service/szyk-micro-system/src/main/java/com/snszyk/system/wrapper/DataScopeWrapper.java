/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.entity.DataScope;
import com.snszyk.system.enums.DictEnum;
import com.snszyk.system.vo.DataScopeVO;

import java.util.Objects;


/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class DataScopeWrapper extends BaseEntityWrapper<DataScope, DataScopeVO> {

	public static DataScopeWrapper build() {
		return new DataScopeWrapper();
	}

	@Override
	public DataScopeVO entityVO(DataScope dataScope) {
		DataScopeVO dataScopeVO = Objects.requireNonNull(BeanUtil.copy(dataScope, DataScopeVO.class));
		String scopeTypeName = DictCache.getValue(DictEnum.DATA_SCOPE_TYPE, dataScope.getScopeType());
		dataScopeVO.setScopeTypeName(scopeTypeName);
		return dataScopeVO;
	}

}
