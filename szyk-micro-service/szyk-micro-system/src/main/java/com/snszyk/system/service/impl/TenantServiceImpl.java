/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.constant.TenantConstant;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tenant.TenantId;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DesUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.sidas.basic.feign.IBasicClient;
import com.snszyk.sidas.basic.vo.DeviceVO;
import com.snszyk.system.cache.ParamCache;
import com.snszyk.system.entity.*;
import com.snszyk.system.mapper.DictBizMapper;
import com.snszyk.system.mapper.MenuMapper;
import com.snszyk.system.mapper.TenantMapper;
import com.snszyk.system.service.*;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.enums.UserEnum;
import com.snszyk.system.user.feign.IUserClient;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.RoleVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.snszyk.common.constant.TenantConstant.*;
import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class TenantServiceImpl extends BaseServiceImpl<TenantMapper, Tenant> implements ITenantService {

	private final TenantId tenantId;
	private final IRoleService roleService;
	private final IMenuService menuService;
	private final IDeptService deptService;
	private final IPostService postService;
	private final IRoleMenuService roleMenuService;
	private final IDictBizService dictBizService;
	private final IUserClient userClient;
	private final IBasicClient basicClient;
	private final ITenantPackageService tenantPackageService;
	private final MenuMapper menuMapper;
	private final DictBizMapper dictBizMapper;

	@Override
	public IPage<Tenant> selectTenantPage(IPage<Tenant> page, Tenant tenant) {
		return page.setRecords(baseMapper.selectTenantPage(page, tenant));
	}

	@Override
	public Tenant getByTenantId(String tenantId) {
		return getOne(Wrappers.<Tenant>query().lambda().eq(Tenant::getTenantId, tenantId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submitTenant(Tenant tenant) {
		if (Func.isEmpty(tenant.getId())) {
			List<Tenant> tenants = baseMapper.selectList(Wrappers.<Tenant>query().lambda().eq(Tenant::getIsDeleted, SzykConstant.DB_NOT_DELETED));
			List<String> codes = tenants.stream().map(Tenant::getTenantId).collect(Collectors.toList());
			String tenantId = getTenantId(codes);
			tenant.setTenantId(tenantId);
			// 获取参数配置的账号额度
			int accountNumber = Func.toInt(ParamCache.getValue(ACCOUNT_NUMBER_KEY), DEFAULT_ACCOUNT_NUMBER);
			tenant.setAccountNumber(accountNumber);
			// 新建租户对应的默认角色
			Role role = new Role();
			role.setTenantId(tenantId);
			role.setParentId(SzykConstant.TOP_PARENT_ID);
			role.setRoleName("管理员");
			role.setRoleAlias("admin");
			role.setSort(2);
			role.setIsDeleted(SzykConstant.DB_NOT_DELETED);
			roleService.save(role);
			// 新建租户对应的角色菜单权限
			LinkedList<Menu> userMenus = new LinkedList<>();
			// 获取参数配置的默认菜单集合，逗号隔开
			List<String> menuCodes = Func.toStrList(ParamCache.getValue(ACCOUNT_MENU_CODE_KEY));
			List<Menu> menus = getMenus((!menuCodes.isEmpty() ? menuCodes : MENU_CODES), userMenus);
			List<RoleMenu> roleMenus = new ArrayList<>();
			menus.forEach(menu -> {
				RoleMenu roleMenu = new RoleMenu();
				roleMenu.setMenuId(menu.getId());
				roleMenu.setRoleId(role.getId());
				roleMenus.add(roleMenu);
			});
			roleMenuService.saveBatch(roleMenus);
			// 新建租户对应的默认部门
			Dept dept = new Dept();
			dept.setTenantId(tenantId);
			dept.setParentId(SzykConstant.TOP_PARENT_ID);
			dept.setAncestors(String.valueOf(SzykConstant.TOP_PARENT_ID));
			dept.setDeptName(tenant.getTenantName());
			dept.setFullName(tenant.getTenantName());
			dept.setDeptCategory(1);
			dept.setSort(1);
			dept.setIsDeleted(SzykConstant.DB_NOT_DELETED);
			deptService.save(dept);
			// 新建租户对应的默认岗位
			Post post = new Post();
			post.setTenantId(tenantId);
			post.setCategory(1);
			post.setPostCode("ceo");
			post.setPostName("首席执行官");
			post.setSort(1);
			postService.save(post);
			// 新建租户对应的默认管理用户
			User user = new User();
			user.setTenantId(tenantId);
			user.setName(tenant.getTenantName() + "超级管理员");
			user.setRealName(tenant.getTenantName() + "超级管理员");
			user.setAccount(tenant.getTenantName());
			// 获取参数配置的密码
			String password = Func.toStr(ParamCache.getValue(PASSWORD_KEY), DEFAULT_PASSWORD);
			user.setPassword(password);
			user.setRoleId(String.valueOf(role.getId()));
			user.setDeptId(String.valueOf(dept.getId()));
			user.setPostId(String.valueOf(post.getId()));
			user.setSex(1);
			user.setUserType(UserEnum.WEB.getCategory());
			user.setIsDeleted(SzykConstant.DB_NOT_DELETED);
			boolean temp = super.saveOrUpdate(tenant);
			R<Boolean> result = userClient.saveUser(user);
			if (!result.isSuccess()) {
				throw new ServiceException(result.getMsg());
			}
			//新增设备树顶级项
			DeviceVO device = new DeviceVO();
			device.setCode(Func.toStr(dept.getId()));
			device.setName(tenant.getTenantName());
			device.setTenantId(tenantId);
			device.setLevel(EolmConstant.Device.TOP_LEVEL);
			// 诊断+润滑
			device.setType(3);
			basicClient.addDevice(device);
			return temp;
		} else {
			CacheUtil.clear(SYS_CACHE, tenant.getTenantId());
			return super.saveOrUpdate(tenant);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeTenant(List<Long> ids) {
		List<String> tenantIds = this.list(Wrappers.<Tenant>query().lambda().in(Tenant::getId, ids))
			.stream().map(tenant -> Func.toStr(tenant.getTenantId())).distinct().collect(Collectors.toList());
		CacheUtil.clear(SYS_CACHE, tenantIds);
		if (tenantIds.contains(SzykConstant.ADMIN_TENANT_ID)) {
			throw new ServiceException("不可删除管理租户!");
		}
		boolean tenantTemp = this.deleteLogic(ids);
		R<Boolean> result = userClient.removeUser(StringUtil.join(tenantIds));
		if (!result.isSuccess()) {
			throw new ServiceException(result.getMsg());
		}
		return tenantTemp;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public DelResultVO checkAndRemoveTenant(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.forEach(id -> {
			//查询租户信息
			Tenant tenant = this.getById(id);
			//租户不存在，或者是管理租户
			if (tenant == null || SzykConstant.ADMIN_TENANT_ID.equals(tenant.getTenantId())) {
				//删除失败记录
				resultVO.getDetailVOList()
					.add(new DelDetailVO(id.toString(),Boolean.FALSE, tenant == null ? "租户不存在" : "管理租户不可删除"));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//查询是否有机构引用
			List<Dept> refs = deptService.list(Wrappers.<Dept>query().lambda()
				.in(Dept::getTenantId, Collections.singletonList(tenant.getTenantId())).eq(Dept::getIsDeleted, 0));
			if (CollectionUtil.isEmpty(refs)) {
				//如果无机构引用
				//清除租户缓存
				CacheUtil.clear(SYS_CACHE, tenant.getTenantId());
				//逻辑删除此租户
				this.deleteLogic(Collections.singletonList(id));
				//逻辑删除关联用户
				userClient.removeUser(tenant.getTenantId());
				//删除成功记录
				resultVO.getDetailVOList().add(new DelDetailVO(tenant.getTenantName(),Boolean.TRUE, "删除成功"));
				//成功次数+1
				resultVO.setSuccessNumber(resultVO.getSuccessNumber() + 1);
			} else {
				//存在机构引用不允许删除，收集引用的机构名称放入失败提示信息
				Set<String> refNameSet = refs.stream().map(Dept::getDeptName).collect(Collectors.toSet());
				//删除失败记录
				resultVO.getDetailVOList().add(new DelDetailVO(tenant.getTenantName(),Boolean.FALSE, "租户信息被机构引用，无法删除，具体如下："
					+ StringUtil.collectionToDelimitedString(refNameSet, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}

	@Override
	public boolean setting(Integer accountNumber, Date expireTime, String ids) {
		List<String> tenantIds = this.list(Wrappers.<Tenant>query().lambda().in(Tenant::getId, ids))
			.stream().map(tenant -> Func.toStr(tenant.getTenantId())).distinct().collect(Collectors.toList());
		CacheUtil.clear(SYS_CACHE, tenantIds);
		Func.toLongList(ids).forEach(id -> {
			Kv kv = Kv.create().set("accountNumber", accountNumber).set("expireTime", expireTime).set("id", id);
			String licenseKey = DesUtil.encryptToHex(JsonUtil.toJson(kv), DES_KEY);
			update(
				Wrappers.<Tenant>update().lambda()
					.set(Tenant::getAccountNumber, accountNumber)
					.set(Tenant::getExpireTime, expireTime)
					.set(Tenant::getLicenseKey, licenseKey)
					.eq(Tenant::getId, id)
			);
		});
		return true;
	}

	@Override
	public boolean packageSetting(String tenantId, Long packageId) {
		new Thread(new Runnable() {
			@Override
			public void run() {
				Tenant tenant = getOne(Wrappers.<Tenant>query().lambda().eq(Tenant::getTenantId,tenantId));
				TenantPackage tenantPackage = tenantPackageService.getById(packageId);
				RoleVO roleVO = roleService.selectByRoleAlias(tenant.getTenantId(), RoleConstant.ADMIN);

				//删除PC关联菜单
				if (!Func.isEmpty(tenant.getPackageId())){
					List<Menu> allMenus = menuMapper.allMenu("PC");
					if (CollectionUtil.isEmpty(allMenus)){
						return;
					}
					List<RoleMenu> list = roleMenuService.list(Wrappers.<RoleMenu>query().lambda().eq(RoleMenu::getRoleId, roleVO.getId()));
					if (CollectionUtil.isNotEmpty(list)){
						Map<Long, Menu> menuMap = allMenus.stream().collect(Collectors.toMap(Menu::getId, Function.identity()));
						List<Long> ids = list.stream().filter(m->menuMap.containsKey(m.getMenuId())).map(RoleMenu::getId).collect(Collectors.toList());
						roleMenuService.removeByIds(ids);
					}
				}

				// 获取参数配置的默认菜单集合，逗号隔开
				List<String> menuCodes = Func.toStrList(ParamCache.getValue(TenantConstant.ACCOUNT_MENU_CODE_KEY));
				menuCodes = menuCodes.size() > 0 ? menuCodes : TenantConstant.MENU_CODES;
				List<Long> menuIds = Func.toLongList(tenantPackage.getMenuId());
				List<String> settingMenuCodes = menuService.list(Wrappers.<Menu>query().lambda().in(Menu::getId, menuIds)).stream().map(Menu::getCode).collect(Collectors.toList());
				for (String defaultMenuCode : menuCodes) {
					if (!settingMenuCodes.contains(defaultMenuCode)){
						settingMenuCodes.add(defaultMenuCode);
					}
				}
				LinkedList<Menu> userMenus = new LinkedList<>();
				List<Menu> menus = getMenus(settingMenuCodes, userMenus);
				List<RoleMenu> roleMenus = new ArrayList<>();
				menus.forEach(menu -> {
					RoleMenu roleMenu = new RoleMenu();
					roleMenu.setMenuId(menu.getId());
					roleMenu.setRoleId(roleVO.getId());
					roleMenus.add(roleMenu);
				});
				roleMenuService.saveBatch(roleMenus);
			}
		}).start();
		return update(Wrappers.<Tenant>update().lambda().set(Tenant::getPackageId, packageId).eq(Tenant::getTenantId, tenantId));
	}

	private String getTenantId(List<String> codes) {
		String code = tenantId.generate();
		if (codes.contains(code)) {
			return getTenantId(codes);
		}
		return code;
	}

	private List<Menu> getMenus(List<String> codes, LinkedList<Menu> menus) {
		codes.forEach(code -> {
			Menu menu = menuService.getOne(Wrappers.<Menu>query().lambda().eq(Menu::getCode, code).eq(Menu::getIsDeleted, SzykConstant.DB_NOT_DELETED));
			if (menu != null) {
				menus.add(menu);
				recursionMenu(menu.getId(), menus);
			}
		});
		return menus;
	}

	private void recursionMenu(Long parentId, LinkedList<Menu> menus) {
		List<Menu> menuList = menuService.list(Wrappers.<Menu>query().lambda().eq(Menu::getParentId, parentId).eq(Menu::getIsDeleted, SzykConstant.DB_NOT_DELETED));
		menus.addAll(menuList);
		menuList.forEach(menu -> recursionMenu(menu.getId(), menus));
	}

	private List<DictBiz> getDictBizs(String tenantId, LinkedList<DictBiz> dictBizs) {
		List<DictBiz> dictBizList = dictBizService.list(Wrappers.<DictBiz>query().lambda()
			.eq(DictBiz::getParentId, SzykConstant.TOP_PARENT_ID)
			.eq(DictBiz::getIsDeleted, SzykConstant.DB_NOT_DELETED));
		dictBizList.forEach(dictBiz -> {
			Long oldParentId = dictBiz.getId();
			Long newParentId = IdWorker.getId();
			dictBiz.setId(newParentId);
			dictBizs.add(dictBiz);
			recursionDictBiz(tenantId, oldParentId, newParentId, dictBizs);
		});
		return dictBizs;
	}

	private void recursionDictBiz(String tenantId, Long oldParentId, Long newParentId, LinkedList<DictBiz> dictBizs) {
		List<DictBiz> dictBizList = dictBizService.list(Wrappers.<DictBiz>query().lambda()
			.eq(DictBiz::getParentId, oldParentId)
			.eq(DictBiz::getIsDeleted, SzykConstant.DB_NOT_DELETED));
		dictBizList.forEach(dictBiz -> {
			Long oldSubParentId = dictBiz.getId();
			Long newSubParentId = IdWorker.getId();
			dictBiz.setId(newSubParentId);
			dictBiz.setParentId(newParentId);
			dictBizs.add(dictBiz);
			recursionDictBiz(tenantId, oldSubParentId, newSubParentId, dictBizs);
		});
	}


}
