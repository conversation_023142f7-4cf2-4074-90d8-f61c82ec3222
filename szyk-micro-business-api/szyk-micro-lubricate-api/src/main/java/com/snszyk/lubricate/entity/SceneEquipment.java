/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 润滑设备场景关联表实体类
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@Accessors(chain = true)
@TableName("lubricate_scene_equipment")
@ApiModel(value = "EquipmentScene对象", description = "润滑设备场景关联表")
public class SceneEquipment implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 设备ID
	 */
	@ApiModelProperty(value = "设备ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long equipmentId;
	/**
	 * 场景ID
	 */
	@ApiModelProperty(value = "场景ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long sceneId;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
