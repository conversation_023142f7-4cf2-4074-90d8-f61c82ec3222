/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PLC编码类型枚举类
 *
 * <AUTHOR>
 * @date 2024-04-03
 **/
@Getter
@AllArgsConstructor
public enum PlcConfigCodeEnum {

	/**
	 * 阀组开关状态
	 */
	VR_SWITCH_STATUS("vr_switch_status", "阀组开关状态"),
	/**
	 * 阀组故障状态
	 */
	VR_FAULT_STATUS("vr_fault_status", "阀组故障状态"),
	/**
	 * 系统模式
	 */
	SR_MODE("sr_mode", "系统模式自动读"),
	/**
	 * 系统模式
	 */
	SW_MODE_AUTO("sw_mode_auto", "系统模式（自动）"),
	/**
	 * 系统模式
	 */
	SW_MODE_MANUAL("sw_mode_manual", "系统模式（手动）"),
	/**
	 * 系统启动
	 */
	SR_START("sr_start", "系统启动"),
	/**
	 * 系统启动（启动）
	 */
	SW_START_ON("sw_start_on", "系统启动（启动）"),
	/**
	 * 系统启动（停止）
	 */
	SW_START_OFF("sw_start_off", "系统启动（停止）"),
	/**
	 * 系统故障状态
	 */
	SR_FAULT_STATUS("sr_fault_status", "系统故障状态"),
	/**
	 * 润滑泵启动状态
	 */
	PR_START_STATUS("pr_start_status", "润滑泵启动状态"),
	/**
	 * 润滑泵故障状态
	 */
	PR_FAULT_STATUS("pr_fault_status", "润滑泵故障状态"),
	/**
	 * 阀组开
	 */
	VW_ON("vw_on", "阀组开"),
	/**
	 * 阀组关
	 */
	VW_OFF("vw_off", "阀组关"),
	/**
	 * 润滑泵启动
	 */
	PW_ON("pw_on", "润滑泵启动"),
	/**
	 * 润滑泵停止
	 */
	PW_OFF("pw_off", "润滑泵停止"),
	/**
	 * 阀组供油次数
	 */
	VR_OIL_SUPPLY("vr_oil_supply", "阀组供油剩余次数"),
	/**
	 * 阀组循环时间
	 */
	VR_RECYCLE_TIME("vr_recycle_time", "阀组循环剩余时间"),
	/**
	 * 阀组供油次数设定
	 */
	VW_OIL_SUPPLY("vw_oil_supply", "阀组供油次数设定"),
	/**
	 * 阀组供油次数设定
	 */
	VR_OIL_SUPPLY_SET("vr_oil_supply_set", "阀组供油次数设定"),
	/**
	 * 阀组循环时间设定
	 */
	VW_RECYCLE_TIME("vw_recycle_time", "阀组循环时间设定"),
	/**
	 * 阀组循环时间设定
	 */
	VR_RECYCLE_TIME_SET("vr_recycle_time_set", "阀组循环时间设定"),
	/**
	 * 系统运行时间（时）
	 */
	SR_RUN_TIME_HOUR("sr_run_time_hour", "系统运行时间（时）本次"),
	/**
	 * 系统运行时间（分）
	 */
	SR_RUN_TIME_MINUTE("sr_run_time_minute", "系统运行时间（分）本次"),
	/**
	 * 系统运行时间（秒）
	 */
	SR_RUN_TIME_SECOND("sr_run_time_second", "系统运行时间（秒）本次"),
	/**
	 * 系统油压
	 */
	SR_OIL_PRESSURE("sr_oil_pressure", "系统油压"),
	/**
	 * 当前油位
	 */
	SR_OIL_LEVEL("sr_oil_level", "当前油位"),
	/**
	 * 润滑泵运行压力上限读
	 */
	PR_PRESSURE_UPPER("pr_pressure_upper", "润滑泵运行压力上限读"),
	/**
	 * 润滑泵运行压力下限读
	 */
	PR_PRESSURE_LOWER("pr_pressure_lower", "润滑泵运行压力下限读"),
	/**
	 * 润滑泵运行报警压力上限读
	 */
	PR_PRESSURE_ALARM_UPPER("pr_pressure_alarm_upper", "润滑泵运行报警压力上限读"),
	/**
	 * 润滑泵运行报警压力下限读
	 */
	PR_PRESSURE_ALARM_LOWER("pr_pressure_alarm_lower", "润滑泵运行报警压力下限读"),
	/**
	 * 润滑泵运行报警压力上限读
	 */
	PR_PRESSURE_RANGE_UPPER("pr_pressure_range_upper", "润滑泵压力表量程上限读"),
	/**
	 * 润滑泵运行报警压力下限读
	 */
	PR_PRESSURE_RANGE_LOWER("pr_pressure_range_lower", "润滑泵压力表量程下限读"),
	/**
	 * 润滑泵当前压力
	 */
	PR_PRESSURE("pr_pressure", "润滑泵当前压力读"),
	/**
	 * 润滑泵运行压力上限写
	 */
	PW_PRESSURE_UPPER("pw_pressure_upper", "润滑泵运行压力上限写"),
	/**
	 * 润滑泵运行压力下限写
	 */
	PW_PRESSURE_LOWER("pw_pressure_lower", "润滑泵运行压力下限写"),
	/**
	 * 润滑泵运行压力上限写
	 */
	PW_PRESSURE_ALARM_UPPER("pw_pressure_alarm_upper", "润滑泵运行报警压力上限写"),
	/**
	 * 润滑泵运行压力下限写
	 */
	PW_PRESSURE_ALARM_LOWER("pw_pressure_alarm_lower", "润滑泵运行报警压力下限写"),
	/**
	 * 润滑泵运行压力上限写
	 */
	PW_PRESSURE_RANGE_UPPER("pw_pressure_range_upper", "润滑泵压力表量程上限写"),
	/**
	 * 润滑泵运行压力下限写
	 */
	PW_PRESSURE_RANGE_LOWER("pw_pressure_range_lower", "润滑泵压力表量程下限写"),
	/**
	 * 润滑泵当前压力
	 */
	PW_PRESSURE("pw_pressure", "润滑泵当前压力写"),
	/**
	 * 润滑泵当前压力
	 */
	SR_RUN_TIME_DAY_TOTAL("sr_run_time_day_total", "系统运行时间（天）总"),
	/**
	 * 润滑泵当前压力
	 */
	SR_RUN_TIME_HOUR_TOTAL("sr_run_time_hour_total", "系统运行时间（时）总"),
	/**
	 * 润滑泵当前压力
	 */
	SR_RUN_TIME_MINUTE_TOTAL("sr_run_time_minute_total", "系统运行时间（分）总"),
	/**
	 * 润滑泵当前压力
	 */
	SR_RUN_TIME_SECOND_TOTAL("sr_run_time_second_total", "系统运行时间（秒）总"),
	/**
	 * 管道压力报警
	 */
	PLR_PRESSURE_ALARM("plr_pressure_alarm", "管道压力报警"),
	;

	final String code;
	final String name;

	public static PlcConfigCodeEnum getByCode(String code){
		for (PlcConfigCodeEnum value : PlcConfigCodeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
