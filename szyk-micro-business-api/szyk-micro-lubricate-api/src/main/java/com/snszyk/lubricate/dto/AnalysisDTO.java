/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.lubricate.entity.Analysis;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 润滑分析表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AnalysisDTO extends Analysis {
	private static final long serialVersionUID = 1L;

	/**
	 * 场景名称
	 */
	@ApiModelProperty(value = "场景名称")
	private String sceneName;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 点位名称
	 */
	@ApiModelProperty(value = "点位名称")
	private String pointName;

	/**
	 * 点位路径
	 */
	@ApiModelProperty(value = "点位路径")
	private String pointPath;

	/**
	 * 润滑不良等级
	 */
	@ApiModelProperty(value = "润滑不良等级")
	private String alarmLevelName;

}
