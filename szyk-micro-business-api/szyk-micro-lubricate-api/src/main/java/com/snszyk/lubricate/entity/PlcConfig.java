/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PLC配置信息表实体类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@TableName("lubricate_plc_config")
@ApiModel(value = "PLC_CONFIG对象", description = "PLC配置信息表")
public class PlcConfig extends TenantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "系统ID")
	private Long systemId;
	/**
	 * IP地址
	 */
	@ApiModelProperty(value = "IP地址")
	private String ipAddress;
	/**
	 * 端口号
	 */
	@ApiModelProperty(value = "端口号")
	private Integer port;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
