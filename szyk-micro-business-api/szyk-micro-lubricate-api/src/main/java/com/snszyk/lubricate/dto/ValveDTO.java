/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.dto;

import com.snszyk.lubricate.entity.Valve;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阀门表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ValveDTO extends Valve {
	private static final long serialVersionUID = 1L;

	/**
	 * 阀门路径
	 */
	@ApiModelProperty(value = "阀门路径")
	private String pathName;

}
