/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.lubricate.entity.PlcConfig;
import com.snszyk.lubricate.entity.PlcMemory;
import com.snszyk.lubricate.entity.System;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * PLC信息配置表视图实体类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlcConfigVO对象", description = "PLC信息配置表")
public class PlcConfigVO extends PlcConfig {
	private static final long serialVersionUID = 1L;

	/**
	 * PLC配置明细
	 */
	@ApiModelProperty(value = "PLC配置明细")
	private List<PlcMemoryVO> plcMemoryList;

	/**
	 * 系统名称
	 */
	@ApiModelProperty(value = "系统名称")
	private String systemName;

}
