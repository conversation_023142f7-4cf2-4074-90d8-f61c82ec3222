/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.vo;

import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.lubricate.entity.PlcAlarm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 润滑报警表视图实体类
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmVO对象", description = "润滑报警表")
public class PlcAlarmVO extends PlcAlarm {
	private static final long serialVersionUID = 1L;

	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	public PlcAlarmVO(){
		super();
	}

	public PlcAlarmVO(Long systemId){
		super();
		this.setSystemId(systemId);
		this.setAlarmTime(DateUtil.now());
	}

}
