/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 每周几枚举类
 *
 * <AUTHOR>
 * @date 2024/06/13 15:56
 **/
@Getter
@AllArgsConstructor
public enum WeekDateEnum {

	/**
	 * 水平超限
	 */
	MONDAY("MON", "周一"),
	/**
	 * 水平低限
	 */
	TUESDAY("TUE", "周二"),
	/**
	 * 窗内
	 */
	WEDNESDAY("WED", "周三"),
	/**
	 * 窗外
	 */
	THURSDAY("THU", "周四"),
	/**
	 * 窗外
	 */
	FRIDAY("FRI", "周五"),
	/**
	 * 窗外
	 */
	SATURDAY("SAT", "周六"),
	/**
	 * 窗外
	 */
	SUNDAY("SUN", "周日"),
	;

	final String code;
	final String name;

	public static WeekDateEnum getByCode(String code){
		for (WeekDateEnum value : WeekDateEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
