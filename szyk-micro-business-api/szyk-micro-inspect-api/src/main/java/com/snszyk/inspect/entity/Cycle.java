/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备点检周期实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@Accessors(chain = true)
@TableName("inspect_cycle")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Cycle对象", description = "设备点检周期")
public class Cycle extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 周期编号
	 */
	@ApiModelProperty(value = "周期编号")
	private String no;
	/**
	 * 周期名称
	 */
	@ApiModelProperty(value = "周期名称")
	private String name;
	/**
	 * 周期类型(班检：team、日检：day、周检：week、月检：month)
	 */
	@ApiModelProperty(value = "周期类型(班检：team、日检：day、周检：week、月检：month)")
	private String type;
	/**
	 * 周期间隔(周期类型为日检、周检、月检时为1-100的正整数，默认值为1)
	 */
	@ApiModelProperty(value = "周期间隔(周期类型为日检、周检、月检时为1-100的正整数，默认值为1)")
	private Integer cycleInterval;
	/**
	 * 执行日期（周检：MON、TUE 、WED、THU、FRI、SAT、SUN，月检：1号-28号）
	 */
	@ApiModelProperty(value = "执行日期（周检：MON、TUE 、WED、THU、FRI、SAT、SUN，月检：1号-28号）")
	private String executeDate;
	/**
	 * 班次定义
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "班次定义")
	private String groupDefinition;

}
