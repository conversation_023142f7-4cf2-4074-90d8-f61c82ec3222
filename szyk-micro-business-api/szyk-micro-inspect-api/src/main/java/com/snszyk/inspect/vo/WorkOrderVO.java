/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.inspect.entity.WorkOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备点检工单视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WorkOrderVO对象", description = "设备点检工单")
public class WorkOrderVO extends WorkOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 父级ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父级ID")
	private Long parentId;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startDate;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endDate;

	/**
	 * 点检人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "点检人")
	private Long inspectUser;

	public WorkOrderVO(){
		super();
	}

	public WorkOrderVO(Long taskId, Long planId, Long cycleId, Long equipmentId, Integer sort){
		super();
		this.setNo(BizCodeUtil.generate("IO"));
		this.setTaskId(taskId);
		this.setPlanId(planId);
		this.setCycleId(cycleId);
		this.setEquipmentId(equipmentId);
		this.setSort(sort);
		this.setStatus(0);
		this.setCreateTime(DateUtil.now());
	}

}
