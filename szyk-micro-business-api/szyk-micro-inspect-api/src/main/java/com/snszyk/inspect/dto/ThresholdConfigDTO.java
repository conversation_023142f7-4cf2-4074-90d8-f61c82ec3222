/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.dto;

import com.snszyk.inspect.entity.ThresholdConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备点检门限配置数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ThresholdConfigDTO extends ThresholdConfig {
	private static final long serialVersionUID = 1L;

	/**
	 * 指标名称
	 */
	@ApiModelProperty(value = "指标名称")
	private String quotaName;

	/**
	 * 门限类型
	 */
	@ApiModelProperty(value = "门限类型")
	private String typeName;

}
