/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点检结果状态枚举类
 *
 * <AUTHOR>
 * @date 2024/06/17 15:56
 **/
@Getter
@AllArgsConstructor
public enum ResultStatusEnum {

	/**
	 * 暂存
	 */
	DRAFT(0, "暂存"),
	/**
	 * 保存
	 */
	SAVE(1, "保存"),
	/**
	 * 提交
	 */
	SUBMIT(2, "提交"),
	;

	final Integer code;
	final String name;

	public static ResultStatusEnum getByCode(Integer code){
		for (ResultStatusEnum value : ResultStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
