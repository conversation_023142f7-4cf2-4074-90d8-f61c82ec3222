/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.inspect.entity.WorkOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 设备点检工单数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WorkOrderDTO extends WorkOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String equipmentModel;

	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String equipmentPath;

	/**
	 * 计划名称
	 */
	@ApiModelProperty(value = "计划名称")
	private String planName;

	/**
	 * 执行日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "执行日期")
	private Date executeDate;

	/**
	 * 执行日期
	 */
	@ApiModelProperty(value = "执行日期")
	private String executeTime;

	/**
	 * 点检人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "点检人")
	private Long inspectUser;

	/**
	 * 点检人
	 */
	@ApiModelProperty(value = "点检人")
	private String inspectUserName;

	/**
	 * 点检部门
	 */
	@ApiModelProperty(value = "点检部门")
	private String inspectDeptName;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 是否异常（0：正常，1：异常，2关闭）
	 */
	@ApiModelProperty(value = "是否异常（0：正常，1：异常，2关闭）")
	private String abnormalStatus;

	/**
	 * 任务信息
	 */
	@ApiModelProperty(value = "任务信息")
	private TaskDTO taskInfo;

	/**
	 * 点检结果列表
	 */
	@ApiModelProperty(value = "点检结果列表")
	private List<MonitorInspectDTO> inspectResultList;

	/**
	 * 异常关闭原因
	 */
	@ApiModelProperty(value = "异常关闭原因")
	private String closeReasonName;

	/**
	 * 设备是否可选
	 */
	@ApiModelProperty(value = "设备是否可选")
	private Boolean canSelect;

}
