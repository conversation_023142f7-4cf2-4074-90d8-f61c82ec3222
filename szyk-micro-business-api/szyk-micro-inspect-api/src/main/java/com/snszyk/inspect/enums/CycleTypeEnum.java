/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点检周期类型枚举类
 *
 * <AUTHOR>
 * @date 2024/06/13 15:56
 **/
@Getter
@AllArgsConstructor
public enum CycleTypeEnum {

	/**
	 * 班检
	 */
	GROUP("GROUP", "班检"),
	/**
	 * 日检
	 */
	DAY("DAY", "日检"),
	/**
	 * 周检
	 */
	WEEK("WEEK", "周检"),
	/**
	 * 月检
	 */
	MONTH("MONTH", "月检"),
	;

	final String code;
	final String name;

	public static CycleTypeEnum getByCode(String code){
		for (CycleTypeEnum value : CycleTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
