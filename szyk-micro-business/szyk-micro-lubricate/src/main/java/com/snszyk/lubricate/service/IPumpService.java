/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.lubricate.dto.PumpDTO;
import com.snszyk.lubricate.dto.PumpDataDTO;
import com.snszyk.lubricate.entity.Pump;
import com.snszyk.lubricate.vo.PumpVO;

import java.util.List;

/**
 * 润滑泵表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IPumpService extends BaseService<Pump> {

	/**
	 * 分页
	 *
	 * @param page
	 * @param pump
	 * @return
	 */
	IPage<PumpDTO> page(IPage<PumpDTO> page, PumpVO pump);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	PumpVO detail(Long id);

	/**
	 * 提交
	 *
	 * @param pump
	 * @return
	 */
	boolean submit(PumpVO pump);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	boolean remove(List<Long> ids);

	/**
	 * 备泵替换主泵
	 *
	 * @param mainId
	 * @param backupId
	 * @return
	 */
	boolean replace(Long mainId, Long backupId);

	/**
	 * 润滑泵工况数据
	 *
	 * @param systemId
	 * @param pumpId
	 * @return
	 */
	PumpDataDTO operatingMode(Long systemId, Long pumpId);

}
