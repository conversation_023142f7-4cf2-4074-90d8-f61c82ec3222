/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.lubricate.dto.PlcAlarmDTO;
import com.snszyk.lubricate.entity.PlcAlarm;
import com.snszyk.lubricate.service.IPlcAlarmService;
import com.snszyk.lubricate.vo.PlcAlarmVO;
import com.snszyk.lubricate.wrapper.PlcAlarmWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 润滑报警表 控制器
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/alarm")
@Api(value = "润滑报警表", tags = "润滑报警表接口")
public class PlcAlarmController extends SzykController {

	private final IPlcAlarmService alarmService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入alarm")
	public R<PlcAlarmVO> detail(PlcAlarm alarm) {
		PlcAlarm detail = alarmService.getOne(Condition.getQueryWrapper(alarm));
		return R.data(PlcAlarmWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 润滑报警表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemId", value = "系统id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "type", value = "报警类型", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入alarm")
	public R<IPage<PlcAlarmDTO>> page(@ApiIgnore PlcAlarmVO alarm, Query query) {
		IPage<PlcAlarmDTO> pages = alarmService.page(Condition.getPage(query), alarm);
		return R.data(pages);
	}

	/**
	 * 新增 润滑报警表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入alarm")
	public R save(@Valid @RequestBody PlcAlarm alarm) {
		return R.status(alarmService.save(alarm));
	}

	/**
	 * 修改 润滑报警表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入alarm")
	public R update(@Valid @RequestBody PlcAlarm alarm) {
		return R.status(alarmService.updateById(alarm));
	}

	/**
	 * 新增或修改 润滑报警表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入alarm")
	public R submit(@Valid @RequestBody PlcAlarm alarm) {
		return R.status(alarmService.saveOrUpdate(alarm));
	}


	/**
	 * 删除 润滑报警表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(alarmService.removeByIds(Func.toLongList(ids)));
	}


}
