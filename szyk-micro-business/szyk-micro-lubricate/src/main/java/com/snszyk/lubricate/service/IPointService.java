/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.core.mp.support.Query;
import com.snszyk.lubricate.dto.PointDTO;
import com.snszyk.lubricate.entity.Point;
import com.snszyk.lubricate.vo.BizTreeVO;
import com.snszyk.lubricate.vo.MonitorPointVO;
import com.snszyk.lubricate.vo.PointVO;
import com.snszyk.lubricate.vo.SceneEquipmentVO;
import com.snszyk.sidas.basic.dto.MonitorDTO;

import java.util.List;
import java.util.Map;

/**
 * 润滑点位表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
public interface IPointService extends BaseService<Point> {

	/**
	 * 分页
	 *
	 * @param page
	 * @param point
	 * @return
	 */
	IPage<PointDTO> page(IPage<PointDTO> page, PointVO point);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	PointDTO detail(Long id);

	/**
	 * 提交
	 *
	 * @param point
	 * @return
	 */
	boolean submit(PointVO point);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	boolean remove(List<Long> ids);

	/**
	 * 点位选择设备下的部位分页
	 *
	 * @param query
	 * @param monitorPoint
	 * @return
	 */
	IPage<MonitorDTO> pointSelectMonitorPage(Query query, MonitorPointVO monitorPoint);

	/**
	 * 获取当前单次注油量和注油周期
	 *
	 * @param valveId
	 * @return
	 */
	Map<String, Integer> pointLubricateParam(Long valveId);

	/**
	 * 根据场景id获取设备和点位
	 *
	 * @param sceneId
	 * @return
	 */
	List<BizTreeVO> getEquipmentPoints(Long sceneId);

}
