/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.lubricate.dto.SceneDTO;
import com.snszyk.lubricate.entity.Scene;
import com.snszyk.lubricate.service.ISceneService;
import com.snszyk.lubricate.vo.SceneEquipmentVO;
import com.snszyk.lubricate.vo.SceneVO;
import com.snszyk.lubricate.wrapper.SceneWrapper;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.resource.vo.AttachVO;
import com.snszyk.sidas.basic.dto.EquipmentDTO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 润滑场景表 控制器
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/scene")
@Api(value = "润滑场景表", tags = "润滑场景表接口")
public class SceneController extends SzykController {

	private final ISceneService sceneService;
	private final IAttachClient attachClient;

	/**
	 * 详情 润滑场景表
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<SceneDTO> detail(Long id) {
		return R.data(sceneService.detail(id));
	}

	/**
	 * 分页 润滑场景表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "场景名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入scene")
	public R<IPage<SceneVO>> page(@ApiIgnore SceneVO scene, Query query) {
		QueryWrapper<Scene> queryWrapper = Wrappers.query();
		if(Func.isNotEmpty(scene.getName())){
			queryWrapper.lambda().like(Scene::getName, scene.getName());
		}
		queryWrapper.lambda().orderByDesc(Scene::getCreateTime);
		IPage<Scene> pages = sceneService.page(Condition.getPage(query), queryWrapper);
		return R.data(SceneWrapper.build().pageVO(pages));
	}

	/**
	 * 新增或修改 润滑场景表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入scene")
	public R submit(@Valid @RequestBody SceneVO scene) {
		return R.status(sceneService.submit(scene));
	}

	/**
	 * 删除 润滑场景表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(sceneService.remove(Func.toLongList(ids)));
	}

	/**
	 * 场景选择设备分页 润滑场景表
	 */
	@GetMapping("/sceneSelectEquipmentPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "sceneId", value = "场景id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "场景选择设备分页", notes = "传入scene")
	public R<IPage<EquipmentDTO>> sceneSelectEquipmentPage(@ApiIgnore SceneEquipmentVO sceneEquipment, Query query) {
		return R.data(sceneService.sceneSelectEquipmentPage(query, sceneEquipment));
	}

	/**
	 * 点位选择场景下的设备分页 润滑场景表
	 */
	@GetMapping("/pointSelectEquipmentPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "sceneId", value = "场景id", required = true, paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "点位选择场景下的设备分页", notes = "传入scene")
	public R<IPage<SceneEquipmentVO>> pointSelectEquipmentPage(@ApiIgnore SceneEquipmentVO sceneEquipment, Query query) {
		return R.data(sceneService.pointSelectEquipmentPage(query, sceneEquipment));
	}

	/**
	 * 下拉列表 润滑场景表
	 */
	@GetMapping("/selectList")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "下拉列表", notes = "传入scene")
	public R<List<SceneDTO>> selectList() {
		List<Scene> list = sceneService.list(Wrappers.<Scene>query().lambda().orderByDesc(Scene::getCreateTime));
		if(Func.isNotEmpty(list)){
			List<SceneDTO> resultList = list.stream().map(
				scene -> {
					SceneDTO dto = Objects.requireNonNull(BeanUtil.copy(scene, SceneDTO.class));
					// 图片
					if (Func.isNotEmpty(scene.getImage())) {
						R<Attach> attachR = attachClient.attachInfoById(scene.getImage());
						if (attachR.isSuccess()) {
							dto.setImageInfo(Objects.requireNonNull(BeanUtil.copy(attachR.getData(), AttachVO.class)));
						}
					}
					return dto;
				}
			).collect(Collectors.toList());
			return R.data(resultList);
		}
		return R.data(new ArrayList<>());
	}

}
