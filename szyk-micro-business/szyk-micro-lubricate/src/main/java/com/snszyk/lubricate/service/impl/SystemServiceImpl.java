/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.lubricate.dto.SystemDTO;
import com.snszyk.lubricate.dto.SystemDataDTO;
import com.snszyk.lubricate.entity.System;
import com.snszyk.lubricate.entity.*;
import com.snszyk.lubricate.enums.PlcConfigCodeEnum;
import com.snszyk.lubricate.mapper.SystemMapper;
import com.snszyk.lubricate.service.*;
import com.snszyk.lubricate.service.logic.PLCDataInitLogicService;
import com.snszyk.lubricate.vo.SystemVO;
import com.snszyk.lubricate.wrapper.PumpWrapper;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 润滑系统表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Service
@AllArgsConstructor
public class SystemServiceImpl extends BaseServiceImpl<SystemMapper, System> implements ISystemService {

	private final IPumpService pumpService;
	private final IValveService valveService;
	private final IPointService pointService;
	private final IPlcConfigService plcConfigService;
	private final IPlcMemoryService plcMemoryService;
	private final RedisTemplate redisTemplate;

	@Override
	public SystemDTO detail(Long id) {
		System system = this.getById(id);
		if(system == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		SystemDTO detail = Objects.requireNonNull(BeanUtil.copy(system, SystemDTO.class));;
		List<Pump> list = pumpService.list(Wrappers.<Pump>query().lambda()
			.eq(Pump::getSystemId, system.getId()).orderByAsc(Pump::getSort));
		if(Func.isNotEmpty(list)){
			detail.setPumpList(PumpWrapper.build().listVO(list));
		}
		return detail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(SystemVO vo) {
		// 校验名称唯一性
		System entity = this.getOne(Wrappers.<System>query().lambda()
			.eq(System::getName, vo.getName()).ne(Func.isNotEmpty(vo.getId()), System::getId, vo.getId()));
		if(Func.isNotEmpty(entity)){
			throw new ServiceException("已存在相同名称的润滑系统！");
		}
		System system = Objects.requireNonNull(BeanUtil.copy(vo, System.class));
		system.setSort(this.count() + 1);
		return this.saveOrUpdate(system);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean remove(List<Long> ids) {
		//ids.forEach(id -> {
		//	List<Pump> list = pumpService.list(Wrappers.<Pump>update().lambda().eq(Pump::getSystemId, id));
		//	if(Func.isNotEmpty(list)){
		//		System system = this.getById(id);
		//		throw new ServiceException(system.getName() + "已绑定润滑泵，请先解除关联！");
		//	}
		//});
		//
		// 解除系统的所有阀门与点位的绑定关系
		ids.forEach(id -> {
			List<Valve> valves = valveService.list(Wrappers.<Valve>query().lambda().eq(Valve::getSystemId, id));
			if(Func.isNotEmpty(valves)){
				List<Long> valveIds = valves.stream().map(Valve::getId).collect(Collectors.toList());
				pointService.update(Wrappers.<Point>update().lambda()
					.set(Point::getValveId, null).in(Point::getValveId, valveIds));
			}
		});
		// 同步删除系统下的阀门
		valveService.remove(Wrappers.<Valve>query().lambda().in(Valve::getSystemId, ids));
		// 同步删除系统下的泵
		pumpService.remove(Wrappers.<Pump>query().lambda().in(Pump::getSystemId, ids));
		// 同步删除系统下的PLC配置
		List<PlcConfig> configList = plcConfigService.list(Wrappers.<PlcConfig>query().lambda().in(PlcConfig::getSystemId, ids));
		if(Func.isNotEmpty(configList)){
			List<Long> configIds = configList.stream().map(PlcConfig::getId).collect(Collectors.toList());
			plcMemoryService.remove(Wrappers.<PlcMemory>query().lambda().in(PlcMemory::getConfigId, configIds));
			plcConfigService.remove(Wrappers.<PlcConfig>query().lambda().in(PlcConfig::getSystemId, ids));
		}
		return this.deleteLogic(ids);
	}

	@Override
	public SystemDataDTO operatingMode(Long systemId) {
		SystemDataDTO dto = new SystemDataDTO(systemId);
		PlcConfig plcConfig = plcConfigService.getOne(Wrappers.<PlcConfig>query().lambda().eq(PlcConfig::getSystemId, systemId));
		if(Func.isNotEmpty(plcConfig)){
			List<PlcMemory> plcMemoryList = plcMemoryService.list(Wrappers.<PlcMemory>query().lambda()
				.eq(PlcMemory::getConfigId, plcConfig.getId()));
			if(Func.isNotEmpty(plcMemoryList)){
				Map<String, Object> dataMap = PLCDataInitLogicService.redisAttribteData(plcConfig.getIpAddress(), redisTemplate);
				List<PlcMemory> plcMemory = plcMemoryList.stream().filter(m ->
					m.getConfigCode().contains(PlcConfigCodeEnum.SR_START.getCode())).collect(Collectors.toList());
				if(Func.isNotEmpty(plcMemory)){
					if(Func.isNotEmpty(dataMap.get(plcMemory.get(0).getConfigCode()))){
						Boolean status = (Boolean)dataMap.get(plcMemory.get(0).getConfigCode());
						dto.setStatus(status ? "启动" : "停止");
					}
				}
				plcMemory = plcMemoryList.stream().filter(m ->
					m.getConfigCode().contains(PlcConfigCodeEnum.SR_MODE.getCode())).collect(Collectors.toList());
				if(Func.isNotEmpty(plcMemory)){
					if(Func.isNotEmpty(dataMap.get(plcMemory.get(0).getConfigCode()))){
						Boolean mode = (Boolean)dataMap.get(plcMemory.get(0).getConfigCode());
						dto.setMode(mode ? "自动" : "手动");
					}
				}
				plcMemory = plcMemoryList.stream().filter(m ->
					m.getConfigCode().contains(PlcConfigCodeEnum.SR_FAULT_STATUS.getCode())).collect(Collectors.toList());
				if(Func.isNotEmpty(plcMemory)){
					if(Func.isNotEmpty(dataMap.get(plcMemory.get(0).getConfigCode()))){
						Boolean faultStatus = (Boolean)dataMap.get(plcMemory.get(0).getConfigCode());
						dto.setFaultStatus(faultStatus ? "故障" : "正常");
					}
				}
				plcMemory = plcMemoryList.stream().filter(m ->
					m.getConfigCode().contains(PlcConfigCodeEnum.SR_OIL_PRESSURE.getCode())).collect(Collectors.toList());
				if(Func.isNotEmpty(plcMemory)){
					if(Func.isNotEmpty(dataMap.get(plcMemory.get(0).getConfigCode()))){
						Integer oilPressure = (Integer)dataMap.get(plcMemory.get(0).getConfigCode());
						dto.setOilPressure(oilPressure);
					}
				}
				plcMemory = plcMemoryList.stream().filter(m ->
					m.getConfigCode().contains(PlcConfigCodeEnum.SR_OIL_LEVEL.getCode())).collect(Collectors.toList());
				if(Func.isNotEmpty(plcMemory)){
					if(Func.isNotEmpty(dataMap.get(plcMemory.get(0).getConfigCode()))){
						Integer oilLevel = (Integer)dataMap.get(plcMemory.get(0).getConfigCode());
						dto.setOilLevel(oilLevel);
					}
				}
			}
		}
		return dto;
	}

}
