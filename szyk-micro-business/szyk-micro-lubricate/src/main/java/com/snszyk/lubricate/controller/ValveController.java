/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.lubricate.dto.ValveDTO;
import com.snszyk.lubricate.entity.Valve;
import com.snszyk.lubricate.service.IValveService;
import com.snszyk.lubricate.vo.ValveVO;
import com.snszyk.lubricate.wrapper.ValveWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 阀门表 控制器
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/valve")
@Api(value = "阀门表", tags = "阀门表接口")
public class ValveController extends SzykController {

	private final IValveService valveService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<ValveVO> detail(Long id) {
		Valve detail = valveService.detail(id);
		return R.data(ValveWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 阀门表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "systemId", value = "系统ID", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "pumpId", value = "润滑泵ID", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "name", value = "润滑点位名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入valve")
	public R<IPage<ValveDTO>> page(@ApiIgnore ValveVO valve, Query query) {
		return R.data(valveService.page(Condition.getPage(query), valve));
	}

	/**
	 * 新增 阀门表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入valve")
	public R save(@Valid @RequestBody Valve valve) {
		return R.status(valveService.save(valve));
	}

	/**
	 * 修改 阀门表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "修改", notes = "传入valve")
	public R update(@Valid @RequestBody Valve valve) {
		return R.status(valveService.updateById(valve));
	}

	/**
	 * 新增或修改 阀门表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "新增或修改", notes = "传入valve")
	public R submit(@Valid @RequestBody Valve valve) {
		return R.status(valveService.saveOrUpdate(valve));
	}

	/**
	 * 删除 阀门表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(valveService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 点位选择阀门分页 阀门表
	 */
	@GetMapping("/valveSelectPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "pointId", value = "点位id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "name", value = "阀门名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "点位选择阀门分页", notes = "传入valve")
	public R<IPage<ValveDTO>> valveSelectPage(@ApiIgnore ValveVO valve, Query query) {
		return R.data(valveService.valveSelectPage(query, valve));
	}

	/**
	 * 根据泵选择阀门列表 阀门表
	 */
	@GetMapping("/selectValveList")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "根据泵选择阀门列表", notes = "传入pumpId")
	public R<List<ValveVO>> selectValveList(@ApiParam(value = "润滑泵id", required = true) @RequestParam Long pumpId) {
		List<Valve> list = valveService.list(Wrappers.<Valve>query().lambda()
			.eq(Valve::getPumpId, pumpId).orderByDesc(Valve::getCreateTime));
		if(Func.isNotEmpty(list)){
			return R.data(ValveWrapper.build().listVO(list));
		}
		return R.data(new ArrayList<>());
	}

	/**
	 * 根据系统选择阀门列表 阀门表
	 */
	@GetMapping("/systemValveList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据系统选择阀门列表", notes = "传入systemId")
	public R<List<ValveVO>> systemValveList(@ApiParam(value = "系统id", required = true) @RequestParam Long systemId) {
		List<Valve> list = valveService.list(Wrappers.<Valve>query().lambda()
			.eq(Valve::getSystemId, systemId).orderByDesc(Valve::getCreateTime));
		if(Func.isNotEmpty(list)){
			return R.data(ValveWrapper.build().listVO(list));
		}
		return R.data(new ArrayList<>());
	}

}
