/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.lubricate.entity.Strategy;
import com.snszyk.lubricate.vo.StrategyVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.enums.DictBizEnum;

import java.util.Objects;

/**
 * 润滑场景表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
public class StrategyWrapper extends BaseEntityWrapper<Strategy, StrategyVO> {

	public static StrategyWrapper build() {
		return new StrategyWrapper();
 	}

	@Override
	public StrategyVO entityVO(Strategy strategy) {
		StrategyVO strategyVO = Objects.requireNonNull(BeanUtil.copy(strategy, StrategyVO.class));
		String alarmLevelName = DictBizCache.getValue(DictBizEnum.ALARM_LEVEL, strategy.getAlarmLevel());
		strategyVO.setAlarmLevelName(alarmLevelName);
		strategyVO.setRefuelCycle(strategyVO.getRefuelCycle()/60).setProposeCycle(strategyVO.getProposeCycle()/60);
		return strategyVO;
	}

}
