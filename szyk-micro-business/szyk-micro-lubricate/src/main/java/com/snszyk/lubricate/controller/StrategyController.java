/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.lubricate.entity.Strategy;
import com.snszyk.lubricate.enums.StrategyStatusEnum;
import com.snszyk.lubricate.service.IStrategyService;
import com.snszyk.lubricate.vo.StrategyVO;
import com.snszyk.lubricate.wrapper.StrategyWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 润滑策略表 控制器
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/strategy")
@Api(value = "润滑策略表", tags = "润滑策略表接口")
public class StrategyController extends SzykController {

	private final IStrategyService strategyService;

	/**
	 * 当前推荐策略
	 */
	@GetMapping("/current")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "当前推荐策略", notes = "传入pointId")
	public R<StrategyVO> current(@ApiParam(value = "点位ID", required = true) @RequestParam Long pointId) {
		Strategy strategy = strategyService.getOne(Wrappers.<Strategy>query().lambda()
			.eq(Strategy::getPointId, pointId).eq(Strategy::getStatus, StrategyStatusEnum.PENDING.getCode()));
		if(Func.isNotEmpty(strategy)){
			return R.data(StrategyWrapper.build().entityVO(strategy));
		}
		return R.data(null);
	}

	/**
	 * 策略推荐历史记录 润滑策略表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "pointId", value = "点位ID", required = true, paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "策略推荐历史记录", notes = "传入strategy")
	public R<IPage<StrategyVO>> page(@ApiIgnore StrategyVO strategy, Query query) {
		return R.data(strategyService.page(Condition.getPage(query), strategy));
	}

	/**
	 * 应用新策略 润滑策略表
	 */
	@PostMapping("/apply")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "应用新策略", notes = "传入strategy")
	public R apply(@ApiParam(value = "策略主键", required = true) @RequestParam Long id) {
		return R.status(strategyService.apply(id));
	}

	/**
	 * 忽略推荐策略 润滑策略表
	 */
	@PostMapping("/ignore")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "忽略推荐策略", notes = "传入strategy")
	public R ignore(@ApiParam(value = "策略主键", required = true) @RequestParam Long id) {
		return R.status(strategyService.ignore(id));
	}

}
