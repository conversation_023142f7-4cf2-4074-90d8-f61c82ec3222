<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.inspect.mapper.ResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="resultResultMap" type="com.snszyk.inspect.entity.Result">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="task_id" property="taskId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="wave_info" property="waveInfo"/>
        <result column="threshold_result" property="thresholdResult"/>
        <result column="mechanism_result" property="mechanismResult"/>
        <result column="fault_type" property="faultType"/>
        <result column="ai_result" property="aiResult"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="resultDTOResultMap" type="com.snszyk.inspect.dto.ResultDTO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="task_id" property="taskId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="wave_info" property="waveInfo"/>
        <result column="threshold_result" property="thresholdResult"/>
        <result column="mechanism_result" property="mechanismResult"/>
        <result column="fault_type" property="faultType"/>
        <result column="ai_result" property="aiResult"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="queryResultList" resultMap="resultDTOResultMap">
        SELECT r.* FROM `inspect_result` r LEFT JOIN `inspect_task` t ON r.task_id = t.id WHERE r.status = 2
        <if test="search.inspectUser != null">
            and t.inspect_user = #{search.inspectUser}
        </if>
        <if test="search.inspectDept != null">
            AND t.inspect_dept = #{search.inspectDept}
        </if>
    </select>

</mapper>
