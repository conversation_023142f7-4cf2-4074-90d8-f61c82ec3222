/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.inspect.dto.StatisticsDTO;
import com.snszyk.inspect.dto.TaskDTO;
import com.snszyk.inspect.entity.Task;
import com.snszyk.inspect.vo.StatisticsSearchVO;
import com.snszyk.inspect.vo.TaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点检任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
public interface TaskMapper extends BaseMapper<Task> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param task
	 * @return
	 */
	List<TaskDTO> page(IPage page, @Param("task") TaskVO task);

	/**
	 * 我已领取任务分页
	 *
	 * @param page
	 * @param task
	 * @return
	 */
	List<TaskDTO> myPage(IPage page, @Param("task") TaskVO task);

	/**
	 * 领取任务排行
	 *
	 * @param search
	 * @return
	 */
	List<StatisticsDTO> receiveTasksRank(@Param("search") StatisticsSearchVO search);

	/**
	 * 所有部门近一年完成任务数
	 *
	 * @param queryMonth
	 * @return
	 */
	List<StatisticsDTO> completedTasks(@Param("queryMonth") String queryMonth);

}
