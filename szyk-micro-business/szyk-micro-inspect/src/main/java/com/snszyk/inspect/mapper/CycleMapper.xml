<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.inspect.mapper.CycleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cycleResultMap" type="com.snszyk.inspect.entity.Cycle">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_date" property="executeDate"/>
        <result column="group_definition" property="groupDefinition"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="cycleDTOResultMap" type="com.snszyk.inspect.dto.CycleDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_date" property="executeDate"/>
        <result column="group_definition" property="groupDefinition"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="page" resultMap="cycleDTOResultMap">
        select * from inspect_cycle where is_deleted = 0
        <if test="cycle.name != null and cycle.name != ''">
            AND `name` like concat('%', #{cycle.name}, '%')
        </if>
        <if test="cycle.status!=null">
            and `status`= #{cycle.status}
        </if>
        <if test="cycle.type!=null">
            and `type` = #{cycle.type}
        </if>
        order by create_time desc
    </select>

</mapper>
