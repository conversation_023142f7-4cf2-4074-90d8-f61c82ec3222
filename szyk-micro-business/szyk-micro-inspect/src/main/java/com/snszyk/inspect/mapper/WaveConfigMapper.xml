<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.inspect.mapper.WaveConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="waveConfigResultMap" type="com.snszyk.inspect.entity.WaveConfig">
        <id column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="sampling_freq" property="samplingFreq"/>
        <result column="sampling_points" property="samplingPoints"/>
        <result column="wave_name" property="waveName"/>
        <result column="sample_data_type" property="sampleDataType"/>
        <result column="vibration_type" property="vibrationType"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <delete id="removeById">
        delete from inspect_wave_config where id = #{id}
    </delete>


</mapper>
