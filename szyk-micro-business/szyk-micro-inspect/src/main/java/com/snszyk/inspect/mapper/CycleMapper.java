/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.mapper;

import com.snszyk.inspect.dto.CycleDTO;
import com.snszyk.inspect.entity.Cycle;
import com.snszyk.inspect.vo.CycleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 设备点检周期 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface CycleMapper extends BaseMapper<Cycle> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cycle
	 * @return
	 */
	List<CycleDTO> page(IPage page, @Param("cycle") CycleVO cycle);

}
