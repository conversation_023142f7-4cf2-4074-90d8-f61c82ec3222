/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.service.impl;

import com.snszyk.inspect.dto.PlanDetailDTO;
import com.snszyk.inspect.entity.PlanDetail;
import com.snszyk.inspect.vo.PlanDetailVO;
import com.snszyk.inspect.mapper.PlanDetailMapper;
import com.snszyk.inspect.service.IPlanDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 设备点检计划明细 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
@AllArgsConstructor
public class PlanDetailServiceImpl extends ServiceImpl<PlanDetailMapper, PlanDetail> implements IPlanDetailService {

	@Override
	public List<PlanDetailDTO> listByPlan(Long planId) {
		return baseMapper.listByPlan(planId);
	}

}
