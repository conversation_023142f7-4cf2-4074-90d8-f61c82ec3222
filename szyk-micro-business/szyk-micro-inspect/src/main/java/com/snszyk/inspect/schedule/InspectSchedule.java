package com.snszyk.inspect.schedule;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.inspect.dto.PlanDTO;
import com.snszyk.inspect.entity.Cycle;
import com.snszyk.inspect.entity.PlanDetail;
import com.snszyk.inspect.entity.Task;
import com.snszyk.inspect.entity.WorkOrder;
import com.snszyk.inspect.enums.CycleTypeEnum;
import com.snszyk.inspect.enums.TaskStatusEnum;
import com.snszyk.inspect.service.*;
import com.snszyk.inspect.vo.GroupScheduleVO;
import com.snszyk.inspect.vo.TaskVO;
import com.snszyk.inspect.vo.WorkOrderVO;
import com.snszyk.inspect.wrapper.TaskWrapper;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.feign.IUserSearchClient;
import com.snszyk.system.vo.RoleVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 点检任务定时器
 *
 * <AUTHOR>
 * @date 2024/06/15 13:56
 **/
@Slf4j
@AllArgsConstructor
@Configuration
@EnableScheduling
public class InspectSchedule {

	private final IPlanService planService;
	private final IPlanDetailService planDetailService;
	private final ICycleService cycleService;
	private final ITaskService taskService;
	private final IWorkOrderService orderService;
	private final ISysClient sysClient;
	private final IUserSearchClient userSearchClient;
	private final IMessageClient messageClient;
    private static final Integer[] TYPE = {0,1};
    private static final String[] cycleTypeArr = {"DAY", "WEEK", "MONTH"};
    private static final String timeFormatStr = "HH:mm";
    private static final String[] roleAliases = {"inspect_monitor", "inspect_staff"};

	/**
	 * 班检(每分钟执行一次)
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/17 15:58
	 */
	@Scheduled(cron = "0 0/1 * * * ?")
	public void groupCheck() {
		log.info("################班检任务生成-START-################");
		List<PlanDTO> planList = planService.getTheDayPlans(DateUtil.now(),CycleTypeEnum.GROUP.getCode());
		List<PlanDTO> executePlanList = new ArrayList<>();
		if (Func.isNotEmpty(planList)){
			for(PlanDTO plan : planList){
				Cycle cycle = cycleService.getById(plan.getCycleId());
				// 生成点检任务时间判断
				String groupDefinition = cycle.getGroupDefinition();
				List<GroupScheduleVO> groups = JSONUtil.toList(groupDefinition, GroupScheduleVO.class);
				if(Func.isNotEmpty(groups)){
					log.info("班次定义==================周期：{}，班次：{}", cycle.getId(), groups);
					for (GroupScheduleVO cycleGroup : groups) {
						//当前是否有开始班次
						if (Func.equals(DateUtils.formatTime(DateUtil.now(), timeFormatStr), cycleGroup.getStartTime())){
							plan.setCycleEndTime(cycleGroup.getEndTime());
							executePlanList.add(plan);
						}
					}
				}
			}
		}
		if(Func.isNotEmpty(executePlanList)){
			for(PlanDTO plan : executePlanList){
				this.generateInspectTasks(plan, TYPE[0]);
			}
		}
		log.info("################班检任务生成-END-################");
	}

	/**
	 * 日检、周检、月检（每日凌晨1点进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/15 15:16
	 */
    @Scheduled(cron = "0 0 1 * * ?")
    //@Scheduled(cron = "0 0/1 * * * ?")
    public void otherInspect() {
		log.info("################日检、周检、月检任务生成-START-################");
        List<PlanDTO> planList = planService.getTheDayPlans(DateUtil.now(),
			CycleTypeEnum.DAY.getCode(), CycleTypeEnum.WEEK.getCode(), CycleTypeEnum.MONTH.getCode());
		List<PlanDTO> executePlanList = new ArrayList<>();
        if (Func.isNotEmpty(planList)){
            for (PlanDTO plan : planList) {
				// 生成点检任务时间判断
				Cycle cycle = cycleService.getById(plan.getCycleId());
				String cycleType = cycle.getType();
				Integer cycleInterval = cycle.getCycleInterval();
				String cycleExecuteDate = cycle.getExecuteDate();
				List<Long> days = new ArrayList<>();
				if (CycleTypeEnum.DAY == CycleTypeEnum.getByCode(cycleType)){
					if(Func.isNotEmpty(plan.getDisableDate())){
						days = InspectPlanSchedule.day(plan.getExecuteDate().getTime(), plan.getDisableDate().getTime(), System.currentTimeMillis(), cycleInterval);
					} else {
						days = InspectPlanSchedule.day(plan.getExecuteDate().getTime(), null, System.currentTimeMillis(), cycleInterval);
					}
				}
				if (CycleTypeEnum.WEEK == CycleTypeEnum.getByCode(cycleType)){
					if(Func.isNotEmpty(plan.getDisableDate())){
						days = InspectPlanSchedule.week(plan.getExecuteDate().getTime(), plan.getDisableDate().getTime(), System.currentTimeMillis(), cycleInterval, cycleExecuteDate);
					} else {
						days = InspectPlanSchedule.week(plan.getExecuteDate().getTime(), null, System.currentTimeMillis(), cycleInterval, cycleExecuteDate);
					}
				}
				if (CycleTypeEnum.MONTH == CycleTypeEnum.getByCode(cycleType)){
					if(Func.isNotEmpty(plan.getDisableDate())){
						days = InspectPlanSchedule.month(plan.getExecuteDate().getTime(), plan.getDisableDate().getTime(), System.currentTimeMillis(), cycleInterval, cycleExecuteDate);
					} else {
						days = InspectPlanSchedule.month(plan.getExecuteDate().getTime(), null, System.currentTimeMillis(), cycleInterval, cycleExecuteDate);
					}
				}
				if (Func.isNotEmpty(days) && DateUtils.dayOfFirstMinute(days.get(days.size() - 1)) == DateUtils.dayOfFirstMinute()){
					executePlanList.add(plan);
				}
            }
			if(Func.isNotEmpty(executePlanList)){
				for(PlanDTO plan : executePlanList){
					this.generateInspectTasks(plan, TYPE[1]);
				}
			}
        }
		log.info("################日检、周检、月检任务生成-END-################");
    }

	/**
	 * 生成点检任务和工单，并发送消息
	 *
	 * @param plan
	 * @param type
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/15 16:16
	 */
	private void generateInspectTasks(PlanDTO plan, Integer type){
		// 获取当前时间
		LocalTime now = LocalTime.now();
		// 定义时分的格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(timeFormatStr);
		// 格式化当前时间
		String startTime;
		String endTime;
		if (Func.equals(TYPE[0], type)){
			startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + now.format(formatter) + ":00";
			endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + plan.getCycleEndTime() + ":00";
		} else {
			startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
			endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 23:59:59";
		}
		TaskVO taskVO = new TaskVO(plan.getId(), plan.getInspectDept(), startTime, endTime);
		Task task = Objects.requireNonNull(BeanUtil.copy(taskVO, Task.class));
		Cycle cycle = cycleService.getById(plan.getCycleId());
		task.setCycleInfo(JSONUtil.toJsonStr(cycle));
		task.setPlanInfo(JSONUtil.toJsonStr(plan));
		taskService.save(task);
		List<PlanDetail> planDetailList = planDetailService.list(Wrappers.<PlanDetail>query().lambda()
			.eq(PlanDetail::getPlanId, plan.getId()));
		List<WorkOrder> orderList = planDetailList.stream().map(planDetail -> {
			WorkOrderVO orderVO = new WorkOrderVO(task.getId(), plan.getId(), plan.getCycleId(),
				planDetail.getEquipmentId(), planDetail.getSort());
			orderVO.setInspectDept(task.getInspectDept());
			return Objects.requireNonNull(BeanUtil.copy(orderVO, WorkOrder.class));
		}).collect(Collectors.toList());
		orderService.saveBatch(orderList);
		taskVO.setId(task.getId());
		this.sendMessage(plan.getTenantId(), Arrays.stream(roleAliases).collect(Collectors.joining(",")),
			MessageBizTypeEnum.EQUIPMENT_INSPECT, taskVO);
	}

	/**
	 * 发送站内信
	 *
	 * @param tenantId
	 * @param roleAliases
	 * @param messageBizType
	 * @param task
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/17 11:51
	 */
	private void sendMessage(String tenantId, String roleAliases, MessageBizTypeEnum messageBizType, TaskVO task){
		log.info("发送点检消息：==================={}", Objects.requireNonNull(BeanUtil.copy(task, Task.class)));
		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("INSPECT");
		messageVo.setSender("INSPECT");
		messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		messageVo.setTitle(messageBizType.getMessage());
		messageVo.setBizType(messageBizType.getCode());
		messageVo.setBizId(Func.toStr(task.getId()));
		messageVo.setContent(JSONUtil.toJsonStr(task));
		// 任务中部门下具有点检管理员和点检员角色的人
		ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		// 根据租户ID获取角色信息（roleAlias = inspect_monitor,inspect_staff）
		R<List<RoleVO>> roleListR = sysClient.getRoleByAliases(tenantId, roleAliases);
		//List<String> userPhones = new ArrayList<>();
		if (roleListR.isSuccess() && Func.isNotEmpty(roleListR.getData())) {
			String roleIds = roleListR.getData().stream().map(role ->
				Func.toStr(role.getId())).distinct().collect(Collectors.joining(","));
			// 根据部门和角色获取用户列表
			R<List<User>> userListR = userSearchClient.listByDeptRole(Func.toStr(task.getInspectDept()), roleIds);
			if (userListR.isSuccess() && Func.isNotEmpty(userListR.getData())) {
				List<ReceiverInfoVo.UserVo> userVoList = userListR.getData().stream().map(user -> {
					ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
					userVo.setId(user.getId());
					userVo.setRealName(user.getRealName());
					return userVo;
				}).collect(Collectors.toList());
				receiverInfoVo.setUserList(userVoList);
				//userPhones = userListR.getData().stream().map(user -> user.getPhone()).collect(Collectors.toList());
			} else {
				log.warn("发送设备点检消息失败：获取用户列表失败！code = {}, msg = {}", userListR.getCode(), userListR.getMsg());
			}
		} else {
			log.warn("发送设备点检消息失败：获取角色信息失败！code = {}, msg = {}", roleListR.getCode(), roleListR.getMsg());
		}
		messageVo.setReceiverInfoVo(receiverInfoVo);
		messageClient.pushMessage(messageVo);
	}

	/**
	 * 班检任务超时提醒（每分钟执行一次）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/18 17:09
	 */
	@Scheduled(cron = "0 0/1 * * * ?")
	public void groupTaskTimeout() {
		log.info("===================班检任务超时-START-===================");
		List<Task> list = new ArrayList<>();
		List<Task> taskList = taskService.list(Wrappers.<Task>query().lambda()
			.eq(Task::getIsTimeout, 0).eq(Task::getStatus, TaskStatusEnum.TO_DO.getCode())
			.like(Task::getCycleInfo, CycleTypeEnum.GROUP.getCode()));
		if(Func.isNotEmpty(taskList)){
			for(Task task : taskList){
				Date inspectTime = task.getEndTime();
				long diffTime = inspectTime.getTime() - DateUtil.now().getTime();
				//long diffDays = TimeUnit.MILLISECONDS.toDays(diffTime);
				//if (diffDays >= cycle.getCycle()) {
				//	taskService.removeById(task.getId());
				//}
				if(diffTime < 0){
					list.add(task);
				}
            }
		}
		if(Func.isNotEmpty(list)){
			for(Task task : list){
				task.setIsTimeout(1);
				Cycle cycle = JSONUtil.toBean(task.getCycleInfo(), Cycle.class);
				this.sendMessage(cycle.getTenantId(), roleAliases[0], MessageBizTypeEnum.INSPECT_TASK_TIMEOUT,
					TaskWrapper.build().entityVO(task));
			}
			taskService.updateBatchById(list);
		}
		log.info("===================班检任务超时-END-===================");
	}

	/**
	 * 日检、周检、月检任务超时提醒（每日凌晨0点进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/6/18 17:58
	 */
	@Scheduled(cron = "0 0 0 * * ?")
	public void otherTaskTimeout() {
		log.info("===================日检、周检、月检任务超时-START-===================");
		List<Task> list = new ArrayList<>();
		List<Task> taskList = taskService.list(Wrappers.<Task>query().lambda()
			.eq(Task::getIsTimeout, 0).eq(Task::getStatus, TaskStatusEnum.TO_DO.getCode())
			.and(wrapper -> {
				List<String> cycleTypes = Arrays.asList(cycleTypeArr);
				cycleTypes.forEach(cycleType -> wrapper.like(Task::getCycleInfo, cycleType).or());
			}));
		if(Func.isNotEmpty(taskList)){
			for(Task task : taskList){
				Date inspectTime = task.getEndTime();
				long diffTime = inspectTime.getTime() - DateUtil.now().getTime();
				//long diffDays = TimeUnit.MILLISECONDS.toDays(diffTime);
				//if (diffDays >= cycle.getCycle()) {
				//	taskService.removeById(task.getId());
				//}
				if(diffTime < 0){
					list.add(TaskWrapper.build().entityVO(task));
				}
			}
		}
		if(Func.isNotEmpty(list)){
			for(Task task : list){
				task.setIsTimeout(1);
				Cycle cycle = JSONUtil.toBean(task.getCycleInfo(), Cycle.class);
				this.sendMessage(cycle.getTenantId(), roleAliases[0], MessageBizTypeEnum.INSPECT_TASK_TIMEOUT,
					TaskWrapper.build().entityVO(task));
			}
			taskService.updateBatchById(list);
		}
		log.info("===================日检、周检、月检任务超时-START-===================");
	}

}
