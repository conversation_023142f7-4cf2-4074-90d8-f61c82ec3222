/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.inspect.entity.Plan;
import com.snszyk.inspect.vo.PlanVO;

import java.util.Objects;

/**
 * 设备点检计划包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public class PlanWrapper extends BaseEntityWrapper<Plan, PlanVO> {

	public static PlanWrapper build() {
		return new PlanWrapper();
 	}

	@Override
	public PlanVO entityVO(Plan plan) {
		PlanVO planVO = Objects.requireNonNull(BeanUtil.copy(plan, PlanVO.class));

		//User createUser = UserCache.getUser(plan.getCreateUser());
		//User updateUser = UserCache.getUser(plan.getUpdateUser());
		//planVO.setCreateUserName(createUser.getName());
		//planVO.setUpdateUserName(updateUser.getName());

		return planVO;
	}

}
