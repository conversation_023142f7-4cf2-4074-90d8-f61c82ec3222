/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.inspect.entity.WorkOrder;
import com.snszyk.inspect.vo.WorkOrderVO;

import java.util.Objects;

/**
 * 设备点检工单包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
public class WorkOrderWrapper extends BaseEntityWrapper<WorkOrder, WorkOrderVO> {

	public static WorkOrderWrapper build() {
		return new WorkOrderWrapper();
 	}

	@Override
	public WorkOrderVO entityVO(WorkOrder order) {
		WorkOrderVO orderVO = Objects.requireNonNull(BeanUtil.copy(order, WorkOrderVO.class));

		//User createUser = UserCache.getUser(order.getCreateUser());
		//User updateUser = UserCache.getUser(order.getUpdateUser());
		//orderVO.setCreateUserName(createUser.getName());
		//orderVO.setUpdateUserName(updateUser.getName());

		return orderVO;
	}

}
