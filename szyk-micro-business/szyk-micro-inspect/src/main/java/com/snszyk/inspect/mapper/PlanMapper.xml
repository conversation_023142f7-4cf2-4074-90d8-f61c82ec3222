<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.inspect.mapper.PlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="planResultMap" type="com.snszyk.inspect.entity.Plan">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="name" property="name"/>
        <result column="no" property="no"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="inspect_dept" property="inspectDept"/>
        <result column="execute_date" property="executeDate"/>
        <result column="disable_date" property="disableDate"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="planDTOResultMap" type="com.snszyk.inspect.dto.PlanDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="name" property="name"/>
        <result column="no" property="no"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="inspect_dept" property="inspectDept"/>
        <result column="dept_name" property="deptName"/>
        <result column="execute_date" property="executeDate"/>
        <result column="disable_date" property="disableDate"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="page" resultMap="planDTOResultMap">
        select plan.*, dept.dept_name from inspect_plan plan left join szyk_dept dept on dept.id = plan.inspect_dept
        where plan.is_deleted = 0
        <if test="plan.keywords!=null and plan.keywords != ''">
            AND (plan.`no` like concat('%',#{plan.keywords},'%') or plan.`name` like
            concat('%',#{plan.keywords},'%'))
        </if>
        <if test="plan.status!=null">
            AND plan.status = #{plan.status}
        </if>
        order by plan.create_time desc
    </select>

    <!--查询当天点检计划-->
    <select id="getTheDayPlans" resultMap="planDTOResultMap">
        SELECT
            p.id,
            p.tenant_id,
            p.`name`,
            p.`no`,
            p.cycle_id,
            p.inspect_dept,
            p.execute_date,
            p.disable_date,
            p.remark,
            p.`status`
        FROM
            inspect_plan p
        JOIN inspect_cycle c ON p.cycle_id = c.id
        WHERE p.`status` = 0
            AND p.execute_date <![CDATA[ <= ]]> #{currentDate, jdbcType=DATE}
            AND c.type in
            <foreach collection="types" index="index" item="item" open="("  separator="," close=")">
                #{item}
            </foreach>
    </select>

</mapper>
