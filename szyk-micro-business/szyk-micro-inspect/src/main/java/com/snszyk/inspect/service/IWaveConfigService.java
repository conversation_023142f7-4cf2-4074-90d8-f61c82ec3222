/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.inspect.dto.WaveConfigMonitorDTO;
import com.snszyk.inspect.dto.WaveConfigDTO;
import com.snszyk.inspect.entity.WaveConfig;
import com.snszyk.inspect.vo.WaveConfigVO;

import java.util.List;

/**
 * 设备点检波形配置 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IWaveConfigService extends BaseService<WaveConfig> {

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	WaveConfigDTO detail(Long id);

	/**
	 * 部位列表
	 *
	 * @param parentId
	 * @param monitorName
	 * @param equipmentType
	 * @return
	 */
	List<WaveConfigMonitorDTO> monitorList(Long parentId, String monitorName, Integer equipmentType);

	/**
	 * 保存
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(WaveConfigVO vo);

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	boolean remove(Long id);

}
