/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.mapper;

import com.snszyk.inspect.dto.ResultDTO;
import com.snszyk.inspect.entity.Result;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.inspect.vo.StatisticsSearchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点检结果 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface ResultMapper extends BaseMapper<Result> {

	/**
	 * 查询点检结果
	 *
	 * @param searchVO
	 * @return
	 */
	List<ResultDTO> queryResultList(@Param("search")StatisticsSearchVO searchVO);

}
