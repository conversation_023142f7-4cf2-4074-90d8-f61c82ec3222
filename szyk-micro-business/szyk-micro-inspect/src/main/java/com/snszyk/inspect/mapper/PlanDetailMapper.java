/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.mapper;

import com.snszyk.inspect.dto.PlanDetailDTO;
import com.snszyk.inspect.entity.PlanDetail;
import com.snszyk.inspect.vo.PlanDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 设备点检计划明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface PlanDetailMapper extends BaseMapper<PlanDetail> {

	/**
	 * 列表
	 *
	 * @param planId
	 * @return
	 */
	List<PlanDetailDTO> listByPlan(Long planId);

}
