package com.snszyk.inspect.schedule;

import com.snszyk.common.utils.DateUtils;
import java.util.*;

/**
 * 计划排期
 *
 * <AUTHOR>
 * @date 2024/06/15 15:27
 **/
public class InspectPlanSchedule {

    private static final String[] WEEK = {"SUN","MON","TUE" ,"WED","THU","FRI","SAT"};


    /**
     * 日检
     * @param executeDate
     * @param disableDate
     * @param endTime
     * @param cycleInterval
     * @return
     */
    public static List<Long> day(Long executeDate,Long disableDate,Long endTime,Integer cycleInterval){
        List<Long> list = new ArrayList<>();
        long l1 = DateUtils.dayOfFirstMinute(executeDate);
        long l3 = DateUtils.dayOfFirstMinute(endTime);
        if (disableDate != null) {
            long l2 = DateUtils.dayOfFirstMinute(disableDate);
            //在执行日期当天禁用
            if (l1 == l2 || l1 == l3) {
                return list;
            }

            if (l2 <= l3) {
                Integer days = DateUtils.betweenDay(l1, l2);
                Calendar cd1 = Calendar.getInstance();
                cd1.setTime(new Date(executeDate));
                list.add(executeDate);
                for (int i = cycleInterval; i < days; i = i + cycleInterval + 1) {
                    cd1.add(Calendar.DAY_OF_MONTH,cycleInterval + 1);
                    list.add(cd1.getTime().getTime());
                }
            }else {
                Integer days = DateUtils.betweenDay(l1, l3);
                Calendar cd1 = Calendar.getInstance();
                cd1.setTime(new Date(executeDate));
                list.add(executeDate);
                for (int i = cycleInterval; i < days; i = i + cycleInterval + 1) {
                    cd1.add(Calendar.DAY_OF_MONTH,cycleInterval + 1);
                    list.add(cd1.getTime().getTime());
                }
            }
        }else {
            if (l1 == l3) {
                list.add(executeDate);
            }else {
                Integer days = DateUtils.betweenDay(l1, l3);
                Calendar cd1 = Calendar.getInstance();
                cd1.setTime(new Date(executeDate));
                list.add(executeDate);
                for (int i = cycleInterval; i < days; i = i + cycleInterval + 1) {
                    cd1.add(Calendar.DAY_OF_MONTH,cycleInterval + 1);
                    list.add(cd1.getTime().getTime());
                }
            }
        }
        return list;
    }

    /**
     * 周检
     * @param executeDate
     * @param disableDate
     * @param endTime
     * @param cycleInterval
     * @param cycleExecuteDate
     * @return
     */
    public static List<Long> week(Long executeDate,Long disableDate,long endTime,Integer cycleInterval,String cycleExecuteDate){
        List<Long> list = new ArrayList<>();

        Calendar cd = Calendar.getInstance();
        cd.setTime(new Date(executeDate));
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        System.out.println(dayOfWeek);

        //星期几（固定执行日期，默认周一）
        int weekDay = 2;
        for (int i = 0; i < WEEK.length; i++) {
            if (WEEK[i].equals(cycleExecuteDate)){
                weekDay = i + 1;
                break;
            }
        }
        /*计算实际第一次执行的具体时间*/
        //点检周期确定的日期，第一个排班时间和计划执行日期之间差值（天）
        int t1 = 0;
        //轮到本周
        if ((dayOfWeek > 1 && dayOfWeek < weekDay) || dayOfWeek == weekDay || weekDay == 1){
            if (weekDay == 1){
                t1 = (7 - dayOfWeek) + weekDay;
            }else {
                t1 = weekDay - dayOfWeek;
            }
        }
        //轮到下周
        if (weekDay > 1 && dayOfWeek != weekDay && (dayOfWeek == 1 || dayOfWeek > weekDay)){
            if (dayOfWeek == 1){
                t1 = weekDay - dayOfWeek;
            }else {
                t1 = (7 - dayOfWeek) + weekDay;
            }
        }
        cd.add(Calendar.DAY_OF_MONTH,t1);
        long firstExecuteTime = cd.getTime().getTime();//第一次执行的具体时间

        long l1 = DateUtils.dayOfFirstMinute(firstExecuteTime);

        long l3 = DateUtils.dayOfFirstMinute(endTime);

        long bigTime = new Long(0);
        long smallTime = l1;
        if (disableDate != null){
            long l2 = DateUtils.dayOfFirstMinute(disableDate);
            if (l1 >= l2 || l1 > l3){
                return list;
            }
            if (l2 < l3){
                bigTime = l2;
            }
            if (l2 >= l3){
                bigTime = l3;
            }
        }else {
            if (l1 > l3){
                return list;
            }
            if (l1 <= l3){
                bigTime = l3;
            }
        }
        int daysBetween = (int)((bigTime - smallTime) / (1000 * 60 * 60 * 24));
        list.add(smallTime);
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(new Date(smallTime));
        if (daysBetween >= 7){
            for (int i = 7*(cycleInterval + 1); i <= daysBetween; i = i + 7*(cycleInterval + 1)) {
                calendar1.add(Calendar.DAY_OF_MONTH,7*(cycleInterval + 1));
                list.add(calendar1.getTime().getTime());
            }
        }
        return list;
    }

    /**
     * 月检
     * @param executeDate
     * @param disableDate
     * @param endTime
     * @param cycleInterval
     * @param cycleExecuteDate
     * @return
     */
    public static List<Long> month(Long executeDate,Long disableDate,Long endTime,Integer cycleInterval,String cycleExecuteDate){
        List<Long> list = new ArrayList<>();
        /*首先计算出第一个班次所在的日期*/

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(executeDate));
        // 获得今天是本月的多少号
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        calendar.set(Calendar.DAY_OF_MONTH,Integer.valueOf(cycleExecuteDate).intValue());


        if (Integer.valueOf(cycleExecuteDate).intValue() < day){
            //下个月
            calendar.add(Calendar.MONTH,1);
        }
        //第一个班次所在的日期
        long firstExecuteTime = calendar.getTime().getTime();

        if (firstExecuteTime > endTime){
            return list;
        }
        long l1 = DateUtils.dayOfFirstMinute(firstExecuteTime);
        long l3 = DateUtils.dayOfFirstMinute(endTime);

        long bigTime = new Long("0");
        if (disableDate != null){
            long l2 = DateUtils.dayOfFirstMinute(disableDate);
            //
            if (l1 > l2 || l1 > l3){
                return  list;
            }
            //
            if (l2 < l3){
                bigTime = l2;
            }
            //
            if (l2 >= l3){
                bigTime = l3;
            }
        }else {
            //
            if (l1 <= l3){
                bigTime = l3;
            }
            //
            if (l1 > l3){
                return list;
            }
        }

        //月份差
        int monthSpace = getMonthSpace(firstExecuteTime, bigTime);

        if (monthSpace == 0){
            list.add(firstExecuteTime);
            return list;
        }

        //排期
        for (int i = cycleInterval; i < monthSpace; i = i + cycleInterval + 1) {
            if (bigTime >= calendar.getTime().getTime()){
                calendar.add(Calendar.MONTH,cycleInterval + 1);
                list.add(calendar.getTime().getTime());
            }
        }
        return list;
    }

    /**
     * 月份间隔
     * @param time1
     * @param time2
     * @return
     */
    private static int getMonthSpace(long time1,long time2){
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(new Date(time1));
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(new Date(time2));
        int result =(cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR)) * 12 + cal1.get(Calendar.MONTH)- cal2.get(Calendar.MONTH);
        System.out.println(result);
        return Math.abs(result);
    }


}
