/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.inspect.dto.WorkOrderDTO;
import com.snszyk.inspect.service.IWorkOrderService;
import com.snszyk.inspect.vo.WorkOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 设备点检工单 控制器
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/workOrder")
@Api(value = "设备点检工单", tags = "设备点检工单接口")
public class WorkOrderController extends SzykController {

	private final IWorkOrderService workOrderService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入order")
	public R<WorkOrderDTO> detail(Long id) {
		return R.data(workOrderService.detail(id));
	}

	/**
	 * 分页 设备点检工单
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "inspectDept", value = "责任部门", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "status", value = "状态（0：未点检，1：已点检）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "isAbnormal", value = "异常状态（仅在异常列表使用，默认-1，异常：1，关闭：2）", paramType = "query", dataType = "Integer"),
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入order")
	public R<IPage<WorkOrderDTO>> page(@ApiIgnore WorkOrderVO workOrder, Query query) {
		IPage<WorkOrderDTO> pages = workOrderService.page(Condition.getPage(query), workOrder);
		return R.data(pages);
	}

	/**
	 * 异常管理分页 设备点检工单
	 */
	@GetMapping("/abnormalPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "inspectDept", value = "责任部门", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "isAbnormal", value = "工单状态（待处理：1，已关闭：2，全部：3（默认））", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "String")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "异常管理分页", notes = "传入order")
	public R<IPage<WorkOrderDTO>> abnormalPage(@ApiIgnore WorkOrderVO workOrder, Query query) {
		IPage<WorkOrderDTO> pages = workOrderService.abnormalPage(Condition.getPage(query), workOrder);
		return R.data(pages);
	}

	/**
	 * 设备管理分页 设备点检工单
	 */
	@GetMapping("/equipmentPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "父级id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "isAbnormal", value = "是否异常", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "设备管理分页", notes = "传入order")
	public R<IPage<WorkOrderDTO>> equipmentPage(@ApiIgnore WorkOrderVO workOrder, Query query) {
		IPage<WorkOrderDTO> pages = workOrderService.equipmentPage(Condition.getPage(query), workOrder);
		return R.data(pages);
	}

	/**
	 * 提交工单 设备点检工单
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "提交工单", notes = "传入workOrder")
	public R submit(@Valid @RequestBody WorkOrderVO workOrder) {
		return R.status(workOrderService.submit(workOrder));
	}

	/**
	 * 关闭异常 设备点检工单
	 */
	@PostMapping("/closeAbnormal")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "关闭异常", notes = "传入workOrder")
	public R closeAbnormal(@Valid @RequestBody WorkOrderVO workOrder) {
		return R.status(workOrderService.closeAbnormal(workOrder));
	}

	/**
	 * 工单暂存结果 设备点检工单
	 */
	@GetMapping("/temporaryDetail")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "工单暂存结果", notes = "传入id")
	public R<WorkOrderDTO> temporaryDetail(Long id) {
		return R.data(workOrderService.temporaryDetail(id));
	}

}
