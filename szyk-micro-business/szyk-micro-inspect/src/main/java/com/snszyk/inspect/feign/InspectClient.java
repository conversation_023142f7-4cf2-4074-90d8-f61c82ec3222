package com.snszyk.inspect.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.inspect.dto.InspectDTO;
import com.snszyk.inspect.entity.PlanDetail;
import com.snszyk.inspect.service.IPlanDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点检模块Feign
 *
 * <AUTHOR>
 */
@NonDS
@Slf4j
@RestController
@AllArgsConstructor
public class InspectClient implements IInspectClient{

	private final IPlanDetailService planDetailService;

	@Override
	@GetMapping(PLAN_EQUIPMENT)
	public R<InspectDTO> planEquipment(Long equipmentId) {
		InspectDTO dto = new InspectDTO().setRemoveEquipment(Boolean.TRUE);
		Integer count = planDetailService.count(Wrappers.<PlanDetail>query().lambda()
			.eq(PlanDetail::getEquipmentId, equipmentId));
		if(count > 0 ){
			dto.setRemoveEquipment(Boolean.FALSE);
		}
		return R.data(dto);
	}

}
