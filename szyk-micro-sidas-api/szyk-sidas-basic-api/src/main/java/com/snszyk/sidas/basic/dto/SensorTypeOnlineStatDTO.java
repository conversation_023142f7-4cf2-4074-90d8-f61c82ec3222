package com.snszyk.sidas.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 传感器在线状态统计对象
 * <AUTHOR>
 */
@Data
@ApiModel("传感器在线状态统计对象")
public class SensorTypeOnlineStatDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 传感器类型数量
	 */
	@ApiModelProperty("传感器类型数量")
	private Integer typeCount;

	/**
	 * 传感器实例数量
	 */
	@ApiModelProperty("传感器实例数量")
	private Integer instanceCount;

	/**
	 * 低电量传感器数量
	 */
	@ApiModelProperty("低电量传感器数量")
	private Integer lowPowerCount;

	/**
	 * 无线传感器在线率
	 */
	@ApiModelProperty("无线传感器在线率")
	private BigDecimal onlineRate;

	public SensorTypeOnlineStatDTO(){

	}

	public SensorTypeOnlineStatDTO(Integer typeCount, Integer instanceCount){
		this.typeCount = typeCount;
		this.instanceCount = instanceCount;
	}

}
