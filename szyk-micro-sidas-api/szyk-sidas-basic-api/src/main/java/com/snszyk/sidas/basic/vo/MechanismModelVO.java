/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.sidas.basic.entity.MechanismModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 机理模型表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MechanismModelVO对象", description = "机理模型表")
public class MechanismModelVO extends MechanismModel {
	private static final long serialVersionUID = 1L;

	/**
	 * 参数集合
	 */
	@ApiModelProperty(value = "参数集合")
	private List<MechanismModelParamVO> modelParamList;

	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;

	public MechanismModelVO(){
		super();
	}

	/**
	 * 构造方法
	 *
	 * @param monitorId
	 * @param modelId
	 */
	public MechanismModelVO(Long monitorId, Long modelId) {
		super();
		this.setId(modelId);
		this.monitorId = monitorId;
	}

}
