/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备点检计划类型枚举类
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum SpotCheckTypeEnum {

	/**
	 * 月检
	 */
	MONTH(1, "月检"),

	/**
	 * 周检
	 */
	WEEK(2, "周检"),

	/**
	 * 日检
	 */
	DAY(3, "日检"),
	;

	final Integer code;
	final String name;

	public static SpotCheckTypeEnum getByCode(Integer code){
		for (SpotCheckTypeEnum value : SpotCheckTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
