/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采样数据类型（全部） 枚举类
 *
 * <AUTHOR>
 * @date 2023/07/07 15:56
 **/
@Getter
@AllArgsConstructor
public enum SampledDataTypeEnum {

	/**
	 * 加速度
	 */
	ACCELERATION("ACCELERATION", "加速度"),

	/**
	 * 速度
	 */
	VELOCITY("VELOCITY", "速度"),

	/**
	 * 位移
	 */
	DISPLACEMENT("DISPLACEMENT", "位移"),

	/**
	 * 应力波
	 */
	STRESS("STRESS", "应力波"),

	/**
	 * 电流
	 */
	ELECTRIC("ELECTRIC", "电流"),

	/**
	 * 转速
	 */
	RPM("RPM", "转速"),

	/**
	 * 设备温度
	 */
	EQUIPMENT_TEMPERATURE("DTEMP", "设备温度"),

	/**
	 * 环境温度
	 */
	ENVIRONMENT_TEMPERATURE("ETEMP", "环境温度"),

	/**
	 * 传感器电量
	 */
	SENSOR_POWER("SPOWER", "传感器电量"),

	/**
	 * 传感器在线状态
	 */
	SENSOR_ONLINE("SONLINE", "传感器在线状态"),

	/**
	 * 设备运行状态
	 */
	EQUIPMENT_RUNNING("ERUNNING", "设备运行状态"),

	/**
	 * 电压
	 */
	VOLTAGE("VOLTAGE", "电压"),

	/**
	 * 未知
	 */
	NOT_SUPPORTED("NOT_SUPPORTED", "未知"),
	;

	final String code;
	final String name;

	public static SampledDataTypeEnum getByCode(String code){
		for (SampledDataTypeEnum value : SampledDataTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return NOT_SUPPORTED;
	}

}
