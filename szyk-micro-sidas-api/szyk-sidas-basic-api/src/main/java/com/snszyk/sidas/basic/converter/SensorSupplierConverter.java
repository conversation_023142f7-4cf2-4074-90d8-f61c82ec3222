package com.snszyk.sidas.basic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 传感器厂家转换器
 * <AUTHOR>
 */
public class SensorSupplierConverter implements Converter<String> {

	private static final String HTZK = "航天智控";
	private static final String YLKJ = "因联科技";
	private static final String LN = "联能";
	private static final String FYST = "飞英思特";

	@Override
	public Class supportJavaTypeKey() {
		return String.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public String convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		String cellValue = cellData.getStringValue();
		if (Objects.equals(HTZK, cellValue)) {
			return "1";
		} else if (Objects.equals(YLKJ, cellValue)) {
			return "2";
		} else if (Objects.equals(LN, cellValue)) {
			return "3";
		} else if (Objects.equals(FYST, cellValue)) {
			return "4";
		}
		return null;
	}

	@Override
	public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if ("1".equals(value)) {
			return new WriteCellData(HTZK);
		} else if ("2".equals(value)) {
			return new WriteCellData(YLKJ);
		} else if ("3".equals(value)) {
			return new WriteCellData(LN);
		} else if ("4".equals(value)) {
			return new WriteCellData(FYST);
		}

		return null;
	}

}
