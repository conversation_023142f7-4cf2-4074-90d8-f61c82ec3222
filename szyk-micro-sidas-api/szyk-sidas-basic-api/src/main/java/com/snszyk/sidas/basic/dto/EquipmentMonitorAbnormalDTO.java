package com.snszyk.sidas.basic.dto;

import com.snszyk.sidas.basic.enums.ModelTypeEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.stream.Collectors;

@Data
public class EquipmentMonitorAbnormalDTO {

	private String name;
	private String code;
	private String suggestion;
	private String abnormalType;

	public String getAbnormalType() {
		String data = Arrays.stream(abnormalType.split(",")).map(s -> ModelTypeEnum.getByCode(Integer.parseInt(s)).getName()).collect(Collectors.joining(","));
		return data;
	}

}
