package com.snszyk.sidas.basic.vo;

import com.snszyk.sidas.basic.entity.SensorData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 传感器数据表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SensorDataVO对象", description = "传感器数据表")
public class SensorDataVO extends SensorData {

	private static final long serialVersionUID = 1L;

	/**
	 * 测点ID
	 */
	@ApiModelProperty(value = "测点ID")
	private Long monitorId;

	/**
	 * 编号，仅应力波传感器
	 */
	@ApiModelProperty(value = "编号，仅应力波传感器")
	private String number;

	/**
	 * 特征图谱展示全部/波形
	 */
	@ApiModelProperty(value = "特征图谱展示全部/波形（0：波形，1：全部）")
	private Integer showWave;

	/**
	 * 查询条件-开始时间
	 */
	@ApiModelProperty(value = "查询条件-开始时间")
	private String startTime;

	/**
	 * 查询条件-结束时间
	 */
	@ApiModelProperty(value = "查询条件-结束时间")
	private String endTime;

	/**
	 * 是否有波形数据
	 */
	@ApiModelProperty("是否有波形数据")
	private Integer hasWaveData;

	/**
	 * 传感器类型
	 */
	@ApiModelProperty(value = "传感器类型")
	private Integer category;

	public SensorDataVO() {

	}

	public SensorDataVO(Long monitorId, Long waveId, String startTime, String endTime) {
		this.setMonitorId(monitorId);
		this.setWaveId(waveId);
		this.setStartTime(startTime);
		this.setEndTime(endTime);
	}

	public SensorDataVO(Long monitorId, Long waveId, Date originTime) {
		this.setMonitorId(monitorId);
		this.setWaveId(waveId);
		this.setOriginTime(originTime);
	}

	public SensorDataVO(Long waveId, Integer alarmLevel) {
		this.setWaveId(waveId);
		this.setAlarmLevel(alarmLevel);
	}

}
