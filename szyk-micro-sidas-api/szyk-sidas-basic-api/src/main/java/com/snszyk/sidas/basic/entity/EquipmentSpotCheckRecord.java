package com.snszyk.sidas.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备点检记录表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("eolm_equipment_spot_check_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentSpotCheckRecord对象", description = "EquipmentSpotCheckRecord对象")
public class EquipmentSpotCheckRecord extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;

	/**
	 * 传感器编码
	 */
	@ApiModelProperty(value = "传感器编码")
	private String sensorCode;

	/**
	 * 振动类型：0 - 非振动；1 - 振动
	 */
	@ApiModelProperty(value = "振动类型：0 - 非振动；1 - 振动")
	private Integer vibrationType;

	/**
	 * 采样数据类型
	 */
	@ApiModelProperty(value = "采样数据类型")
	private String outputType;

	/**
	 * 特征值（有效值）
	 */
	@ApiModelProperty("特征值（有效值）")
	private BigDecimal characteristicValue;

	/**
	 * 时域波形
	 */
	@ApiModelProperty("时域波形")
	private String timeDomainWaveform;

	/**
	 * 采集时间
	 */
	@ApiModelProperty("采集时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date originTime;

}
