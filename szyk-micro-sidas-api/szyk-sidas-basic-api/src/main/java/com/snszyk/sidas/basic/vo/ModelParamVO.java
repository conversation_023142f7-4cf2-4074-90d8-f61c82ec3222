/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机理模型参数视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@ApiModel(value = "ModelParamVO对象", description = "机理模型参数")
public class ModelParamVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 机理模型参数
	 */
	@ApiModelProperty(value = "机理模型参数（字典：model_param）")
	private String param;

	/**
	 * 参数值
	 */
	@ApiModelProperty(value = "参数值")
	private BigDecimal value;

}
