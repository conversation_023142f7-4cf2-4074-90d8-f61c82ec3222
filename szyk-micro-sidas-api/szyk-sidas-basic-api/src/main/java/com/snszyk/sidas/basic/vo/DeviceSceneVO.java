/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.vo;

import com.snszyk.sidas.basic.entity.DeviceScene;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 3D场景表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceSceneVO对象", description = "3D场景表")
public class DeviceSceneVO extends DeviceScene {
	private static final long serialVersionUID = 1L;

}
