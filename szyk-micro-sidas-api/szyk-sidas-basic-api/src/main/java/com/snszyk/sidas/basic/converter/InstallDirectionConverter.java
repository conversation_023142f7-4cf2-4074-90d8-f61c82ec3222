package com.snszyk.sidas.basic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 安装方向转换器 - 0-轴向；1-水平；2-垂直
 * <AUTHOR>
 */
public class InstallDirectionConverter implements Converter<Integer> {

	private static final String AXIAL = "轴向";
	private static final String HORIZONTAL = "水平";
	private static final String VERTICAL = "垂直";

	@Override
	public Class supportJavaTypeKey() {
		return Integer.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public Integer convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		String cellValue = cellData.getStringValue();
		if (Objects.equals(AXIAL, cellValue)) {
			return 0;
		} else if (Objects.equals(HORIZONTAL, cellValue)) {
			return 1;
		} else if (Objects.equals(VERTICAL, cellValue)) {
			return 2;
		}
		return null;
	}

	@Override
	public WriteCellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (value == 0) {
			return new WriteCellData(AXIAL);
		} else if (value == 1) {
			return new WriteCellData(HORIZONTAL);
		} else if (value == 2) {
			return new WriteCellData(VERTICAL);
		}

		return null;
	}

}
