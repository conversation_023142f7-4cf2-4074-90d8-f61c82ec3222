/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 润滑策略配置 视图实体类
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "LubricateStrategyConfigVO对象", description = "润滑策略配置")
public class LubricateStrategyConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 推荐规则
	 */
	@ApiModelProperty(value = "推荐规则")
	private Integer recommendedRule;

	/**
	 * 推荐频率
	 */
	@ApiModelProperty(value = "推荐频率")
	private BigDecimal recommendedFreq;

}
