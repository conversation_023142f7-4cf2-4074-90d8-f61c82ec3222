package com.snszyk.sidas.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.sidas.basic.entity.Wave;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 传感器波形VO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WaveVO对象", description = "传感器波形VO")
public class WaveVO extends Wave {

	private static final long serialVersionUID = 1L;

	/**
	 * 父级id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("父级id")
	private Long parentId;

	/**
	 * 应用设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("应用设备类型")
	private Integer equipmentType;

	/**
	 * 标准门限id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准门限id")
	private Long thresholdId;

}
