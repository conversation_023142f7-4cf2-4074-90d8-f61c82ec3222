/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测量方向枚举类
 *
 * <AUTHOR>
 * @date 2023/08/10 15:56
 **/
@Getter
@AllArgsConstructor
public enum MeasureDirectionEnum {

	/**
	 * 水平
	 */
	HORIZONTAL(1, "水平"),

	/**
	 * 垂直
	 */
	VERTICAL(2, "垂直"),

	/**
	 * 轴向
	 */
	AXIAL(0, "轴向");

	final Integer code;
	final String name;

	public static MeasureDirectionEnum getByCode(Integer code){
		for (MeasureDirectionEnum value : MeasureDirectionEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return MeasureDirectionEnum.AXIAL;
	}

}
