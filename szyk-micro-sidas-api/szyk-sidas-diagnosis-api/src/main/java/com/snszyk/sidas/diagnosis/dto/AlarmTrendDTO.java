package com.snszyk.sidas.diagnosis.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 报警趋势dto
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "报警趋势dto")
public class AlarmTrendDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 日期列表
	 */
	@ApiModelProperty(value = "日期列表")
	private List<String> dateList;

	/**
	 * 报警数量列表
	 */
	@ApiModelProperty(value = "报警数量列表")
	private List<Integer> alarmCountList;

	/**
	 * 设备数量列表
	 */
	@ApiModelProperty(value = "设备数量列表")
	private List<Integer> equipmentCountList;

}
