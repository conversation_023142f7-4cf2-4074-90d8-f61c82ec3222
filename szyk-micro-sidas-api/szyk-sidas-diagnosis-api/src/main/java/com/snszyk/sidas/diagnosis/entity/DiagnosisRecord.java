/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 诊断记录表实体类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@Accessors(chain = true)
@TableName("eolm_diagnosis_record")
@ApiModel(value = "DiagnosisRecord对象", description = "诊断记录表")
public class DiagnosisRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 报警id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警id")
	private Long alarmId;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 诊断类型（字典：diagnosis_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "诊断类型（字典：diagnosis_type）")
	private Integer diagnosisType;
	/**
	 * 报警等级（字典：alarm_level）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警等级（字典：alarm_level）")
	private Integer alarmLevel;
	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	/**
	 * 检维修建议
	 */
	@ApiModelProperty(value = "检维修建议")
	private String suggestion;
	/**
	 * 诊断人
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("诊断人")
	private Long diagnoseUser;
	/**
	 * 诊断时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "诊断时间")
	private Date diagnoseTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
