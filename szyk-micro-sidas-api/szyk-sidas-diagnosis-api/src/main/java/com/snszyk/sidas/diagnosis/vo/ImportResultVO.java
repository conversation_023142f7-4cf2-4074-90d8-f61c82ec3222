package com.snszyk.sidas.diagnosis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ImportResultVO对象", description = "ImportResultVO对象")
public class ImportResultVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "删除成功数量")
	private Integer successNumber;

	@ApiModelProperty(value = "删除失败数量")
	private Integer failureNumber;

	@ApiModelProperty(value = "删除失败信息列表")
	private List<ImportDetailVO> detailVOList;

	public ImportResultVO() {
		this.successNumber = 0;
		this.failureNumber = 0;
		this.detailVOList = new ArrayList<>();
	}
}
