/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 报警门限表实体类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
@Accessors(chain = true)
@TableName("eolm_alarm_threshold")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmThreshold对象", description = "报警门限表")
public class AlarmThreshold extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备分类
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备分类")
	private Integer deviceCategory;
	/**
	 * 设备功率
	 */
	@ApiModelProperty(value = "设备功率")
	private BigDecimal devicePower;
	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String devicePath;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;
	/**
	 * 传感器唯一识别码
	 */
	@ApiModelProperty(value = "传感器唯一识别码")
	private String sensorCode;
	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形id")
	private Long waveId;
	/**
	 * 测量方向
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测量方向")
	private Integer measureDirection;
	/**
	 * 应力波编号
	 */
	@ApiModelProperty(value = "应力波编号")
	private String stressNumber;
	/**
	 * 电流相位
	 */
	@ApiModelProperty(value = "电流相位")
	private String currentPhase;
	/**
	 * 振动类型
	 */
	@ApiModelProperty(value = "振动类型")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer vibrationType;
	/**
	 * 采样数据类型（字典：sampled_data_type）
	 */
	@ApiModelProperty(value = "采样数据类型（字典：sampled_data_type）")
	private String sampleDataType;
	/**
	 * 指标类型（默认有效值）
	 */
	@ApiModelProperty(value = "指标类型（默认有效值）")
	private String quotaType;
	/**
	 * 指标单位
	 */
	@ApiModelProperty(value = "指标单位")
	private String quotaUnit;
	/**
	 * 报警类型（字典：alarm_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警类型（字典：alarm_type）")
	private Integer alarmType;
	/**
	 * 一级门限值下限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "一级门限值下限")
	private BigDecimal firstThresholdLower;
	/**
	 * 一级门限值上限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "一级门限值上限")
	private BigDecimal firstThresholdUpper;
	/**
	 * 二级门限值下限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "二级门限值下限")
	private BigDecimal secondThresholdLower;
	/**
	 * 二级门限值上限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "二级门限值上限")
	private BigDecimal secondThresholdUpper;
	/**
	 * 三级门限值下限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "三级门限值下限")
	private BigDecimal thirdThresholdLower;
	/**
	 * 三级门限值上限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "三级门限值上限")
	private BigDecimal thirdThresholdUpper;
	/**
	 * 四级门限值下限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "四级门限值下限")
	private BigDecimal fourthThresholdLower;
	/**
	 * 四级门限值上限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "四级门限值上限")
	private BigDecimal fourthThresholdUpper;
	/**
	 * 门限类型（1：通用指标门限，2：采样值指标门限，3：特征值指标门限）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "门限类型（1：通用指标门限，2：采样值指标门限，3：特征值指标门限）")
	private Integer type;

}
