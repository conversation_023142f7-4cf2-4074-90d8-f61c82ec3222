/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警关闭原因视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AlarmCloseVO对象", description = "报警关闭原因")
public class AlarmCloseVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	private Long id;

	/**
	 * 关闭原因
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "关闭原因（字典：alarm_close_reason）")
	private Integer closeReason;

	/**
	 * 说明
	 */
	@ApiModelProperty(value = "说明")
	private String description;

	/**
	 * 关闭时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "关闭时间")
	private Date closeTime;

	/**
	 * 关闭人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "关闭人")
	private Long closeUser;

	/**
	 * 关闭人姓名
	 */
	@ApiModelProperty(value = "关闭人姓名")
	private String closeUserName;

}
