/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.dto;

import com.snszyk.resource.entity.Attach;
import com.snszyk.sidas.fault.vo.FaultOnsiteInspectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 故障管理-现场查验数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Data
@ApiModel(value = "FaultOnsiteInspectDTO对象", description = "故障现场查验")
public class FaultOnsiteInspectDTO extends FaultOnsiteInspectVO {
	private static final long serialVersionUID = 1L;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;
}
