/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 故障统计数据
 *
 * <AUTHOR>
 * @since 2023-06-07
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "FaultStatisticDTO对象", description = "FaultStatisticDTO对象")
public class FaultStatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "区域id")
	private Long regionId;

	@ApiModelProperty(value = "组织全称")
	private String orgFullName;

	@ApiModelProperty(value = "组织名称")
	private String orgName;

	@ApiModelProperty(value = "组织路径")
	private String orgPath;

	@ApiModelProperty(value = "设备数")
	private Integer deviceCount;

	@ApiModelProperty(value = "总故障数")
	private Integer faultCount;

	@ApiModelProperty(value = "处理总时长(h)")
	private BigDecimal totalHandleTime;

	@ApiModelProperty(value = "平均处理时长(h)")
	private BigDecimal averageHandleTime;

	@ApiModelProperty(value = "故障状态分布")
	private Map<Integer, Integer> faultStateCount;

	@ApiModelProperty(value = "故障等级分布")
	private Map<Integer, Integer> faultLevelCount;

	@ApiModelProperty(value = "故障来源分布")
	private Map<Integer, Integer> faultSourceCount;

	@ApiModelProperty(value = "智能诊断")
	private Integer intelligentCount;

	@ApiModelProperty(value = "人工生成")
	private Integer artificialCount;

	@ApiModelProperty(value = "是否有子集")
	private Integer hasChildren;

	@ApiModelProperty(value = "区域层级")
	private Integer regionLevel;

	@ApiModelProperty(value = "故障统计图数据")
	private FaultStatisticsChartDTO faultStatisticsChart;

}
