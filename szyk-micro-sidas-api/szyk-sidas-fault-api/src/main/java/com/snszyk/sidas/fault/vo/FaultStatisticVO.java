package com.snszyk.sidas.fault.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统计用查询条件
 *
 * <AUTHOR>
 * @since 2023-06-06
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "FaultStatisticVO对象", description = "FaultStatisticVO对象")
public class FaultStatisticVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;

	/**
	 * 分类
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "分类")
	private Integer category;

	/**
	 * 区域层次
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "区域层次")
	private Integer regionLevel;

	/**
	 * 父级id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父级id")
	private Long parentId;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "区域id")
	private Long regionId;

	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	/**
	 * 是否导出
	 */
	@ApiModelProperty(value = "是否导出")
	private Boolean isExport;

}
