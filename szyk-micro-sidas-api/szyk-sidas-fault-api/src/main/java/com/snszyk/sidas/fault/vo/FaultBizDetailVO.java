/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.vo;

import com.snszyk.sidas.fault.entity.FaultBizDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 故障管理业务明细表视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FaultBizDetailVO对象", description = "故障管理业务明细表")
public class FaultBizDetailVO extends FaultBizDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 现场查验
	 */
	@ApiModelProperty(value = "现场查验")
	private FaultOnsiteInspectVO onsiteInspectVO;
	/**
	 * 维护维修
	 */
	@ApiModelProperty(value = "维护维修")
	private FaultMaintainRepairVO maintainRepairVO;
	/**
	 * 诊断验证
	 */
	@ApiModelProperty(value = "诊断验证")
	private FaultDiagnosisVerifyVO diagnosisVerifyVO;
	/**
	 * 更新的故障等级
	 */
	@ApiModelProperty(value = "更新的故障等级（不更新则为空）")
	private String faultLevel;
	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private String receiver;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
