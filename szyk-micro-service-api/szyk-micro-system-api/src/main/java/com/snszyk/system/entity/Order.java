package com.snszyk.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName("szyk_order")
@Data
public class Order extends TenantEntity {

	/**
	 * 订单号
	 */
	private String num;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 产品ID
	 */
	private Long productId;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 订单日期
	 */
	private Date orderTime;

	/**
	 * 支付方式：1-银行卡；2-支付宝；3-微信；4-其他
	 */
	private Integer paymentType;
}
