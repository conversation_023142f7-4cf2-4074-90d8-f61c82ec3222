package com.snszyk.system.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class OrderDTO extends BaseCrudDto {

	/**
	 * 订单号
	 */
	@ApiModelProperty("订单号")
	private String num;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 价格
	 */
	@ApiModelProperty("价格")
	private BigDecimal price;

	/**
	 * 订单日期
	 */
	@ApiModelProperty("订单日期")
	private Date orderTime;

	/**
	 * 支付方式：1-银行卡；2-支付宝；3-微信；4-其他
	 */
	@ApiModelProperty("支付方式：1-银行卡；2-支付宝；3-微信；4-其他")
	private Integer paymentType;

}

