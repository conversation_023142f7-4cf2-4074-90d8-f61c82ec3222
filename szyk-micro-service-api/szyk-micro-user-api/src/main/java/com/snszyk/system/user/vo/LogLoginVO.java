/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.user.vo;

import com.snszyk.system.user.entity.LogLogin;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户登录日志表视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LogLoginVO对象", description = "用户登录日志表")
public class LogLoginVO extends LogLogin {
	private static final long serialVersionUID = 1L;

	/**
	 * 登录账号
	 */
	@ApiModelProperty("登录账号")
	private String userAccount;
	/**
	 * 用户姓名
	 */
	@ApiModelProperty("用户姓名")
	private String userName;
	/**
	 * 所属角色
	 */
	@ApiModelProperty("所属角色")
	private String roleName;
	/**
	 * 所属部门
	 */
	@ApiModelProperty("所属部门")
	private String deptName;
	/**
	 * 活跃天数
	 */
	@ApiModelProperty("活跃天数")
	private Integer activeDays;

}
