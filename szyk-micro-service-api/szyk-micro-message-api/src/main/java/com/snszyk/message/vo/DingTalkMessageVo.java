package com.snszyk.message.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉消息
 *
 * <AUTHOR>
 * @date 2024/04/29 10:56
 **/
@Data
@ApiModel(value = "DingTalkMessageVo对象", description = "DingTalkMessageVo对象")
public class DingTalkMessageVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 租户ID
	 */
	@ApiModelProperty("租户ID")
	private String tenantId;

	/**
	 * 租户名称
	 */
	@ApiModelProperty("租户名称")
	private String tenantName;

	/**
	 * 业务编码
	 */
	@ApiModelProperty("业务编码")
	private String bizCode;

	/**
	 * 业务类型
	 */
	@ApiModelProperty("业务类型")
	private String bizType;

	/**
	 * 消息内容
	 */
	@ApiModelProperty("消息内容")
	private String content;

	/**
	 * 用户手机号
	 */
	@ApiModelProperty("用户手机号")
	private List<String> userPhones;

	public DingTalkMessageVo() {
		super();
	}

	public DingTalkMessageVo(String tenantId, String tenantName, String bizCode, String bizType, String content) {
		super();
		this.tenantId = tenantId;
		this.tenantName = tenantName;
		this.bizCode = bizCode;
		this.bizType = bizType;
		this.content = content;
	}

}
