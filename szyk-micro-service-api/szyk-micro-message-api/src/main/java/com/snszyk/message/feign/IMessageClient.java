/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.message.feign;

import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.vo.MessagePushVo;
import com.snszyk.message.vo.MessageVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Message Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_MESSAGE_NAME
)
public interface IMessageClient {

	String API_PREFIX = "/client";
	String PUSH_MESSAGE = API_PREFIX + "/message/pushMessage";
	String READ_MESSAGE = API_PREFIX + "/message/readMessage";

	/**
	 * 推送消息
	 *
	 * @param v 消息实体
	 * @return
	 */
	@PostMapping(PUSH_MESSAGE)
	R pushMessage(@RequestBody MessageVo v);

	/**
	 * 消息已读
	 *
	 * @param v
	 * @return
	 */
	@PostMapping(READ_MESSAGE)
	R readMessage(@RequestBody MessagePushVo v);

}
