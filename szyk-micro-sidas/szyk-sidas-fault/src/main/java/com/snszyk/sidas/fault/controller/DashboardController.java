package com.snszyk.sidas.fault.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.sidas.fault.dto.DeviceFaultIntervalDTO;
import com.snszyk.sidas.fault.dto.EquipmentMonitorStatDTO;
import com.snszyk.sidas.fault.dto.FaultStatusDTO;
import com.snszyk.sidas.fault.dto.SpotCheckStatisticsDTO;
import com.snszyk.sidas.fault.entity.FaultBiz;
import com.snszyk.sidas.fault.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 故障模块门户端大屏接口
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard")
@Api(value = "故障模块门户端大屏接口", tags = "故障模块门户端大屏接口")
public class DashboardController extends SzykController {

	private final DashboardService dashboardService;

	/**
	 * 集团驾驶舱-厂区设备故障平均时间间隔
	 */
	@ApiOperation("集团驾驶舱-厂区设备故障平均时间间隔")
	@ApiOperationSupport(order = 1)
	@GetMapping("/group/device-fault-interval")
	public R<DeviceFaultIntervalDTO> deviceFaultInterval(SzykUser szykUser) {
		return dashboardService.groupDeviceFaultInterval(szykUser.getTenantId(), szykUser.getDeptId());
	}

	/**
	 * 厂区驾驶舱-故障状态统计
	 */
	@ApiOperation("厂区驾驶舱-故障状态统计")
	@ApiOperationSupport(order = 2)
	@GetMapping("/plant/fault-status-statistics/{id}")
	public R<FaultStatusDTO> plantFaultStatusStatistics(@PathVariable(name = "id") String id) {
		return dashboardService.plantFaultStatusStatistics(id);
	}

	/**
	 * 厂区驾驶舱-最新待处理故障
	 */
	@ApiOperation("厂区驾驶舱-最新待处理故障")
	@ApiOperationSupport(order = 3)
	@GetMapping("/plant/latest-pending-fault/{id}")
	public R<List<FaultBiz>> plantLatestPendingFault(@PathVariable(name = "id") String id) {
		return dashboardService.plantLatestPendingFault(id);
	}

	/**
	 * 设备监测统计：当月总报警数、总故障数、点检设备数，及相比上月的变化
	 */
	@ApiOperation("设备监测统计")
	@ApiOperationSupport(order = 4)
	@GetMapping("/equipment-monitor-stat")
	public R<EquipmentMonitorStatDTO> equipmentMonitorStat(SzykUser szykUser) {
		return dashboardService.equipmentMonitorStat(szykUser.getTenantId());
	}

	/**
	 * 近N个月的点检统计：点检数、点检设备数
	 */
	@ApiOperation("近N个月的点检统计")
	@ApiOperationSupport(order = 5)
	@GetMapping("/spot-check-statistics")
	public R<SpotCheckStatisticsDTO> getSpotCheckStatistics(Integer lastMonths) {
		return dashboardService.getSpotCheckStatistics(lastMonths);
	}

	/**
	 * 设备故障数量
	 */
	@ApiOperation("设备故障数量")
	@ApiOperationSupport(order = 6)
	@GetMapping("/fault-total")
	public R<Integer> faultTotal(@RequestParam("id") Long id) {
		return R.data(dashboardService.faultTotal(id));
	}
}
