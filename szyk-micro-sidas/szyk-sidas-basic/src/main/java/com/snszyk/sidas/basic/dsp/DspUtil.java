package com.snszyk.sidas.basic.dsp;

import com.alibaba.fastjson.JSONObject;
import com.github.psambit9791.jdsp.misc.UtilMethods;
import com.github.psambit9791.jdsp.signal.Decimate;
import com.github.psambit9791.jdsp.transform.FastFourier;
import com.github.psambit9791.jdsp.transform.Hilbert;
import com.google.common.primitives.Doubles;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.complex.Complex;
import org.apache.commons.math3.transform.DftNormalization;
import org.apache.commons.math3.transform.FastFourierTransformer;
import org.apache.commons.math3.transform.TransformType;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数字信号处理工具类
 * 	通用指标（有效值、峰值、峰峰值、裕度、歪度、峭度）、傅里叶变换、包络频谱、抽样等。
 *
 * <AUTHOR>
 */
@Slf4j
public class DspUtil {

	/**
	 * 计算数组的平均值
	 * @param data 输入数据
	 * @return mean
	 */
	public static double getMean(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		BigDecimal sum = BigDecimal.ZERO;
		for (Double element : data) {
			sum = sum.add(BigDecimal.valueOf(element));
		}

		return sum.divide(BigDecimal.valueOf(data.length), 10, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 计算RMS均方根值（有效值）
	 * @param data 输入数据
	 * @return rms
	 */
	public static double getRMS(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double sum = 0;
		for (double aDouble : data) {
			sum += Math.pow(aDouble, 2);
		}

		return Math.sqrt(sum / data.length);
	}

	/**
	 * 计算峰值 - 振动波形的单峰最大值
	 * @param data 输入数据
	 * @return
	 */
	public static double getPeak(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double peakValue = 0;
		for (double d : data) {
			if (d > peakValue) {
				peakValue = d;
			}
		}

		return peakValue;
	}

	/**
	 * 计算峰峰值 - 最大值和最小值之差
	 * @param data 输入数据
	 * @return
	 */
	public static double getPeakToPeak(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double maxPeakValue = 0;
		double minPeakValue = 0;
		for (double d : data) {
			if (d > maxPeakValue) {
				maxPeakValue = d;
			} else if (d  < minPeakValue) {
				minPeakValue = d;
			}
		}

		return maxPeakValue - minPeakValue;
	}

	/**
	 * 计算裕度 - 峰值绝对值与方根幅值的比值，体现了信号的一种冲击特性。
	 * @param data 输入数据
	 * @return
	 */
	public static double getClearanceFactor(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double maxAbs = 0;
		double sum = 0;
		for (double d : data) {
			double datumAbs = Math.abs(d);
			if (datumAbs > maxAbs) {
				maxAbs = datumAbs;
			}

			sum += Math.sqrt(Math.abs(d));
		}

		BigDecimal divisor = BigDecimal.valueOf(sum)
			.divide(BigDecimal.valueOf(data.length), 10, RoundingMode.HALF_UP).pow(2);
		return BigDecimal.valueOf(maxAbs)
			.divide(divisor, 10, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 计算歪度 - 反应了振动信号的非对称性，通常情况下振动信号是关于x轴对称的，这时候SK应该趋近于0。
	 * @param data 输入数据
	 * @return
	 */
	public static double getSkewnessFactor(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double mean = getMean(data);
		double sum = 0;
		for (double d : data) {
			sum += Math.pow(Math.abs(d) - mean, 3);
		}

		sum = BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(data.length), 10, RoundingMode.HALF_UP).doubleValue();
		BigDecimal divisor = BigDecimal.valueOf(getRMS(data)).pow(3);

		return BigDecimal.valueOf(sum).divide(divisor, 10, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 计算峭度 - 反应了振动信号的冲击特性，峭度对于冲击比较敏感，一般情况下峭度值应该在3左右，
	 * 	因为正态分布的峭度等于3，如果偏离3太多则说明机械设备存在一定的冲击性振动，可能存在某种故障隐患。
	 * @param data 输入数据
	 * @return
	 */
	public static double getKurtosis(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		double mean = getMean(data);
		double sum = 0;
		for (double d : data) {
			sum += Math.pow(Math.abs(d) - mean, 4);
		}

		sum = BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(data.length), 10, RoundingMode.HALF_UP).doubleValue();
		BigDecimal divisor = BigDecimal.valueOf(getRMS(data)).pow(4);

		return BigDecimal.valueOf(sum).divide(divisor, 10, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 计算波峰系数 - 波峰与RMS的比值
	 * @param data 输入数据
	 * @return 波峰系数
	 */
	public static double getCrestFactor(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		return BigDecimal.valueOf(getPeak(data))
			.divide(BigDecimal.valueOf(getRMS(data)), 10, RoundingMode.HALF_UP)
			.doubleValue();
	}

	/**
	 * 通过fft获取频谱幅值数据
	 * @param data 时域波形数据
	 * @return 频谱幅值数据
	 */
	public static double[] fftMagnitude(double[] data) {
		FastFourier fastFourier = new FastFourier(data);
		fastFourier.transform();
		double[] magnitude = fastFourier.getMagnitude(false);

		//取绝对值 & 对幅值缩小（除以N/2） & 取单边谱 - https://www.jianshu.com/p/78043f8f8306
		for (int i = 0; i < magnitude.length; i++) {
			magnitude[i] = Math.abs(magnitude[i] / magnitude.length * 2);
		}
		return Arrays.copyOf(magnitude, magnitude.length / 2);
	}

	/**
	 * 通过fft获取频谱相位数据
	 * @param data 时域波形数据
	 * @return 频谱相位数据
	 */
	public static double[] fftPhaseDeg(double[] data) {
		FastFourier fastFourier = new FastFourier(data);
		fastFourier.transform();
		double[] phaseDeg = fastFourier.getPhaseDeg(false);
		return Arrays.copyOf(phaseDeg, phaseDeg.length / 2);
	}

	/**
	 * 求倒频谱数据
	 *  方式一：时域信号 -> 频谱 -> 对数 -> 傅里叶逆变换
	 *  方式二：时域信号 -> 功率谱 -> 对数 -> 傅里叶逆变换
	 *  其中，求功率谱的2种方法为：
	 *  	1.(傅立叶变换的平方)/(区间长度)；
	 *  	2.自相关函数的傅里叶变换
	 *  频谱、功率谱在经过对数之后，结果只差了2倍的常数，因此对最终波形无影响，所以本系统采用方式一求倒频谱。
	 *  倒谱图的横坐标为时间，单位为ms，范围为时域信号波形横坐标最大值的1/2。
	 *  参考：<a href="https://blog.csdn.net/qq_36002089/article/details/108378796">倒谱</a>
	 *  	<a href="https://zhuanlan.zhihu.com/p/34989414">信号频域分析方法的理解</a>
	 * @param data 时域波形数据
	 * @return 倒谱数据
	 */
	public static double[] cepstrum(double[] data) {
		if (data == null || data.length == 0) {
			return null;
		}

		//将数据长度扩展为2的指数
		double[] dataExt = extendSignal(data);

		// 计算FFT变换
		FastFourierTransformer fft = new FastFourierTransformer(DftNormalization.STANDARD);
		Complex[] fftSpectrum = fft.transform(dataExt, TransformType.FORWARD);

		// 计算对数频谱
		double[] logSpectrum = new double[fftSpectrum.length];
		for (int i = 0; i < fftSpectrum.length; i++) {
			logSpectrum[i] = Math.log10(fftSpectrum[i].abs());
		}

		// 计算倒谱信号
		FastFourierTransformer ifft = new FastFourierTransformer(DftNormalization.STANDARD);
		Complex[] cepstrum = ifft.transform(logSpectrum, TransformType.INVERSE);

		// 取实部得到倒谱
		double[] cepstrumReal = new double[fftSpectrum.length];
		for (int i = 0; i < fftSpectrum.length; i++) {
			cepstrumReal[i] = cepstrum[i].getReal();
		}

		//只取前半部分即可
		return Arrays.copyOf(cepstrumReal, cepstrumReal.length / 2);
	}

	/**
	 * This extends the signal such that length is in the nearest power of 2
	 * 	参考：com.github.psambit9791.jdsp.transform.FastFourier#extendSignal()
	 */
	private static double[] extendSignal(double[] signal) {
		double power = Math.log(signal.length) / Math.log(2);
		double raisedPower = Math.ceil(power);
		int newLength = (int) (Math.pow(2, raisedPower));
		if (newLength != signal.length) {
			return UtilMethods.zeroPadSignal(signal, newLength - signal.length);
		}

		return signal;
	}

	/**
	 * 计算时域波形的包络频谱
	 * @param data 时域波形数据
	 * @return 包络频谱
	 */
	public static double[] envelopeSpectrum(double[] data) {
		if (data == null || data.length == 0) {
			return null;
		}

		//[Python] data = data - np.mean(data)  # 去直流分量

		//去直流分量
		double meanValue = getMean(data);
		double[] dataWithoutMean = new double[data.length];
		for (int i = 0; i < data.length; i++) {
			dataWithoutMean[i] = data[i] - meanValue;
		}

		//[Python] xt = data
		//[Python] ht = fftpack.hilbert(xt)  # 做希尔伯特变换
		//[Python] at = np.sqrt(xt**2+ht**2)   # 获得解析信号at = sqrt(xt^2 + ht^2)

		//hilbert变换 - getAmplitudeEnvelope()输出即为解析信号at
		Hilbert hilbert = new Hilbert(dataWithoutMean);
		hilbert.transform();
		double[] atData = hilbert.getAmplitudeEnvelope();

		//[Python] am = np.fft.fft(at)         # 对解析信号at做fft变换获得幅值
		//[Python] am = np.abs(am)             # 对幅值求绝对值（此时的绝对值很大）
		//[Python] am = am/len(am)*2
		//[Python] am = am[0: int(len(am)/2)]  # 取正频率幅值

		//对atData做fft - 已对幅值缩小 & 取单边谱
		return fftMagnitude(atData);
	}

	/**
	 * 信号抽取
	 * 	<a href="http://matlab.izmiran.ru/help/toolbox/signal/decimate.html">
	 * 	    http://matlab.izmiran.ru/help/toolbox/signal/decimate.html</a>
	 *
	 * @param signal 信号时域数据
	 * @param signalFreq 原始信号的采样频率
	 * @param outputSignalFreq 输出信号的采样频率
	 * @return
	 */
	public static double[] decimate(double[] signal, int signalFreq, int outputSignalFreq) {
		Decimate decimate = new Decimate(signal, signalFreq);
		return decimate.decimate(signalFreq / outputSignalFreq);
	}

	/**
	 * 获取 包络图-轴承第一、二阶段超高频率（Ultra High Frequency）变化识别：
	 * 	1）选择包络图上频率范围f1~f2（20k-60k）之间的前n个纵坐标振幅最大值构成数组M[n]；
	 * 	2）依次遍历数组M[n]，每次用数组中的最大值M[n]max减去数组中M[n]min最小值，然后除以最大值M[n]max，得出比率x，进行比对，
	 * 		如果>=a%，则去掉当前最小值M[n]min，
	 * 		如果<a%，则保留当前最小值M[n]min。
	 * 	直到找出比例在a%范围内的值数组M[x](x <=n)；
	 * 	3）取数组M[x]的平均值，然后根据divide决定是否除以包络图的平均值，得出值r， 模型需设置区间范围[r1, r2]。
	 * 	判断当前结果r与区间范围的关系是否满足条件。
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param envelopWaveform 包络波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：
	 *                     {"maximumArrLength":3,
	 *                     "envelopeFreqUpper":60,
	 *                     "envelopeFreqLower":20,
	 *                     "differentValueRatio":0.2}
	 * @param divide 是否除以包络图的平均值
	 * @return
	 */
	public static double getUHF(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] envelopWaveform, String paramJson, boolean divide) {
		if (envelopWaveform == null || envelopWaveform.length == 0 || StringUtil.isEmpty(paramJson)) {
			log.warn("getUHF - 参数校验失败！");
			return 0d;
		}

		//获取机理模型参数（其中envelopeFreqLower、envelopeFreqUpper 单位为KHz）
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		int envelopeFreqLower = paramObject.getIntValue("envelopeFreqLower");
		int envelopeFreqUpper = paramObject.getIntValue("envelopeFreqUpper");
		int maximumArrLength = paramObject.getIntValue("maximumArrLength");
		double differentValueRatio = paramObject.getDoubleValue("differentValueRatio");

		//校验最大频率是否超过超高频率下限
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(BigDecimal.valueOf(envelopeFreqLower)) <= 0) {
			log.info("getUHF - 当前波形的截止频率(cutoffFreq = {})过低，无UHF(envelopeFreqLower = {})数据。",
				cutoffFreq, envelopeFreqLower);
			return 0d;
		}

		//根据截止频率截取包络数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(envelopWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		envelopWaveform = Arrays.copyOf(envelopWaveform, cutoffLength);

		//获取超高频率范围的包络数据
		int minIndex = BigDecimal.valueOf(envelopeFreqLower)
			.multiply(BigDecimal.valueOf(envelopWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (BigDecimal.valueOf(envelopeFreqUpper).compareTo(cutoffFreqKHz) < 0) {
			maxIndex = BigDecimal.valueOf(envelopeFreqUpper)
				.multiply(BigDecimal.valueOf(envelopWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = envelopWaveform.length;
		}
		double[] uhfData = Arrays.copyOfRange(envelopWaveform, minIndex, maxIndex);
		Arrays.sort(uhfData);

		//获取前N大的幅值数组（从小到大排列）
		double[] topMaxData;
		if (uhfData.length > maximumArrLength) {
			topMaxData = Arrays.copyOfRange(uhfData, (uhfData.length - maximumArrLength), uhfData.length);
		} else {
			topMaxData = uhfData;
		}

		//获取数组中每个值与最大值的差值满足条件<a%的数组
		int startIndex = topMaxData.length - 1;
		for (int i = 0; i < topMaxData.length; i++) {
			BigDecimal subtractRatio = BigDecimal.valueOf(topMaxData[topMaxData.length - 1])
				.subtract(BigDecimal.valueOf(topMaxData[i]))
				.divide(BigDecimal.valueOf(topMaxData[topMaxData.length - 1]), 10, RoundingMode.HALF_UP);
			if (subtractRatio.compareTo(BigDecimal.valueOf(differentValueRatio)) < 0) {
				startIndex = i;
			}
		}
		topMaxData = Arrays.copyOfRange(topMaxData, startIndex, topMaxData.length);

		if (divide) {
			//计算超高频幅值数组的平均值除以包络图的平均值 - 轴承第一阶段超高频率变化识别
			return BigDecimal.valueOf(getMean(topMaxData))
				.divide(BigDecimal.valueOf(getMean(envelopWaveform)), 10, RoundingMode.HALF_UP)
				.doubleValue();
		} else {
			//超高频率幅值数组的平均值 - 轴承第二阶段超高频率变化识别_依赖
			return getMean(topMaxData);
		}
	}

	/**
	 * 获取 包络图-轴承第二阶段轴承固有频率变化识别
	 *  选择包络图上频率范围500-2k （f1-f2）之间的振幅最大值 a。判断a是不是在
	 *    {[转频*齿数1*m1，转频*齿数1*m2],[转频*齿数2*m1，转频*齿数2*m2],[转频*齿数N*m1，转频*齿数N*m2]}
	 * 	范围内，如果a在范围内，则再取该范围内的次大值，以此类推，直到a不在范围内, 得到此时的结果a，
	 * 	然后计算前n1天a的平均值a(n1)，减去前n2天的平均值a(n2)，除以前n2天的平均值a(n2)，得出值b。
	 * 	模型需设置区间范围[b1, b2]。判断当前结果b是否在设置的区间范围内。
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param envelopWaveform 包络波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：
	 *                  {"amplitudeUpper":1.1,
	 *                  "amplitudeLower":0.9,
	 *                  "envelopeFreqUpper":2,
	 * 	                "envelopeFreqLower":0.5,
	 *                  "basePeriodTime":90,
	 *                  "lastPeriodTime":7}
	 * @param rotatingFreq 转频
	 * @param teethList 齿数集合
	 * @return
	 */
	public static double getIntrinsicFreq(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] envelopWaveform, String paramJson,
										  double rotatingFreq, List<Integer> teethList) {
		//参数校验
		if (envelopWaveform == null || envelopWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).compareTo(BigDecimal.ZERO) == 0 || CollectionUtil.isEmpty(teethList)) {
			log.warn("getIntrinsicFreq - 参数校验失败！");
			return 0d;
		}

		//获取机理模型的参数（其中envelopeFreqLower、envelopeFreqUpper 单位为KHz）
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");
		double envelopeFreqLower = paramObject.getDoubleValue("envelopeFreqLower");
		double envelopeFreqUpper = paramObject.getDoubleValue("envelopeFreqUpper");

		//校验最大频率是否超过500Hz
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(BigDecimal.valueOf(envelopeFreqLower)) <= 0) {
			log.info("getIntrinsicFreq - 当前波形的截止频率(cutoffFreq = {})过低，无固有频率下限(envelopeFreqLower={})以上的振幅数据。",
				cutoffFreq, envelopeFreqLower);
			return 0d;
		}

		//根据截止频率截取包络数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(envelopWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		envelopWaveform = Arrays.copyOf(envelopWaveform, cutoffLength);

		//获取固有频率范围内的包络数据
		int minIndex = BigDecimal.valueOf(envelopeFreqLower)
			.multiply(BigDecimal.valueOf(envelopWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (BigDecimal.valueOf(envelopeFreqUpper).compareTo(cutoffFreqKHz) < 0) {
			maxIndex = BigDecimal.valueOf(envelopeFreqUpper)
				.multiply(BigDecimal.valueOf(envelopWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = envelopWaveform.length;
		}
		double[] intrinsicFreqData = Arrays.copyOfRange(envelopWaveform, minIndex, maxIndex);
		Arrays.sort(intrinsicFreqData);

		//从大到小依次判断是否在指定范围内
		BigDecimal startData = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal endData = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));
		for (int i = intrinsicFreqData.length - 1; i > 0; i--) {
			boolean inRange = false;
			for (Integer teethNumber : teethList) {
				BigDecimal startAmplitude = startData.multiply(BigDecimal.valueOf(teethNumber));
				BigDecimal endAmplitude = endData.multiply(BigDecimal.valueOf(teethNumber));
				if (startAmplitude.compareTo(BigDecimal.valueOf(intrinsicFreqData[i])) <= 0
					&& endAmplitude.compareTo(BigDecimal.valueOf(intrinsicFreqData[i])) >= 0) {
					inRange = true;
					break;
				}
			}

			//找到第一个不在指定范围内的幅值
			if (!inRange) {
				return intrinsicFreqData[i];
			}
		}

		return 0d;
	}

	/**
	 *  获取特征频率排名：取每个轴承1倍转频下的特征频率（如：轴承内圈缺陷频率q （BPFI）），转频*q*m1得出q1，转频*q*m2得出q2，
	 *  然后取包络数组中[q1,q2]间的所有纵坐标值振幅形成数组w。取w中最大值和次最大值对应的横坐标x1、 x2, 依次取
	 *  2x1、2x2、 3x1、3x2、4x1、4x2、 5x1、5x2，加上x1, x2共10个横坐标，依次获取这10个横坐标对应的纵坐标值
	 *  y1、y2、2y1、2y2、3y1、3y2、4y1、4y2、5y1、5y2, 并获得每个纵坐标值振幅在包络数组的排名p1, p2, 2p1,
	 *  2p2, 3p1, 3p2, 4p1, 4p2, 5p1, 5p2。从所有区间中取最高值，验证最高值是否在前X名之内。
	 *  如果在前X名之内，则报警。
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param envelopWaveform 包络波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @param characteristicFreqList 轴承特征频率列表：bpfi、bpfo、ftf、bsf
	 * @return
	 */
	public static int getCharacteristicFreqRank(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] envelopWaveform, String paramJson,
												double rotatingFreq, List<BigDecimal> characteristicFreqList) {
		//参数校验
		if (envelopWaveform == null || envelopWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).compareTo(BigDecimal.ZERO) == 0 || CollectionUtil.isEmpty(characteristicFreqList)) {
			log.warn("getCharacteristicFreqRank - 参数校验失败！");
			return 0;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取包络数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(envelopWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		envelopWaveform = Arrays.copyOf(envelopWaveform, cutoffLength);

		//获取包络波形的最大频率
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		//获取多个轴承的特征频率的最大振幅值
		double maxAmplitudeValue = 0;
		BigDecimal startData = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal endData = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));
		for (BigDecimal characteristicFreq : characteristicFreqList) {
			//计算指定频率范围的上下限 - [q1, q2]
			BigDecimal startFreqInterval = startData.multiply(characteristicFreq);
			BigDecimal endFreqInterval = endData.multiply(characteristicFreq);

			//校验最高频率和指定频率范围下限的大小
			if (cutoffFreqKHz.compareTo(startFreqInterval) <= 0) {
				log.info("getCharacteristicFreqRank - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围下限以上(startFreqInterval = {}，characteristicFreq = {})数据。",
					cutoffFreq, startFreqInterval, characteristicFreq);
				continue;
			}

			//获取指定频率范围[q1, q2]的包络数据
			int minIndex = startFreqInterval.multiply(BigDecimal.valueOf(envelopWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
			int maxIndex;
			if (endFreqInterval.compareTo(cutoffFreqKHz) < 0) {
				maxIndex = endFreqInterval.multiply(BigDecimal.valueOf(envelopWaveform.length))
					.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
					.intValue();
			} else {
				maxIndex = envelopWaveform.length;
			}
			double[] intervalData = Arrays.copyOfRange(envelopWaveform, minIndex, maxIndex);

			//获取y1 - 5y1、y2 - 5y2中的最大值
			if (intervalData.length > 1) {
				//保存原始数据副本 & 排序（从小到大）
				double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
				Arrays.sort(intervalData);

				//获取最大值、次大值在原始包络数组中的索引 = 在原始数据副本中的索引 + [q1, q2]的开始索引minIndex
				int maxValueIndex = Doubles.indexOf(srcIntervalData, intervalData[intervalData.length - 1]) + minIndex;
				int secondaryMaxValueIndex = Doubles.indexOf(srcIntervalData, intervalData[intervalData.length - 2]) + minIndex;

				//获取最大值
				for (int i = 1; i <= 5; i++) {
					if ((maxValueIndex * i < envelopWaveform.length)
						&& BigDecimal.valueOf(maxAmplitudeValue).compareTo(BigDecimal.valueOf(envelopWaveform[maxValueIndex * i])) <= 0) {
						maxAmplitudeValue = envelopWaveform[maxValueIndex * i];
					}
					if ((secondaryMaxValueIndex * i < envelopWaveform.length)
						&& BigDecimal.valueOf(maxAmplitudeValue).compareTo(BigDecimal.valueOf(envelopWaveform[secondaryMaxValueIndex * i])) <= 0) {
						maxAmplitudeValue = envelopWaveform[secondaryMaxValueIndex * i];
					}
				}
			}
		}

		//获取maxValue在包络数组中的排名
		if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(maxAmplitudeValue)) != 0) {
			Arrays.sort(envelopWaveform);
			return envelopWaveform.length - Doubles.indexOf(envelopWaveform, maxAmplitudeValue);
		}

		return 0;
	}

	/**
	 * 计算 【频谱】转子摩擦 机理模型值。
	 * （1）计算转频
	 * 		X=转速/60，A×X=X1，B×X=X2，取X1、X2纵坐标最大值得出X3（横坐标的值）
	 * 		X4=2×X3
	 * 		X5=3×X3
	 * 		X6=4×X3
	 * 		X7=5×X3
	 * （2）结果运用
	 * 		X对应的纵坐标为Y
	 * 		取Y3、Y4、Y5、Y6、Y7最小值Y8，计算Y8在所有纵坐标（整个频谱的幅值）中的排名C。
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param frequencyWaveform 频谱
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @return
	 */
	public static int getRotorFriction(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] frequencyWaveform, String paramJson,
									   double rotatingFreq) {
		//参数校验
		if (frequencyWaveform == null || frequencyWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).equals(BigDecimal.ZERO)) {
			log.warn("getRotorFriction - 参数校验失败！");
			return 0;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取频谱数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		frequencyWaveform = Arrays.copyOf(frequencyWaveform, cutoffLength);


		//计算[x1, x2]
		BigDecimal freqStart = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal freqEnd = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));

		//校验频谱的最高评率是否大于X1
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(freqStart) <= 0) {
			log.info("getRotorFriction - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围的数据。", cutoffFreq);
			return 0;
		}

		//获取[x1, x2]范围内的频谱幅值数据
		int minIndex = freqStart.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (freqEnd.compareTo(cutoffFreqKHz) < 0) {
			maxIndex = freqEnd.multiply(BigDecimal.valueOf(frequencyWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = frequencyWaveform.length;
		}

		//保存原始数据副本 & 排序（从小到大）
		double[] intervalData = Arrays.copyOfRange(frequencyWaveform, minIndex, maxIndex);
		if (intervalData.length <= 0) {
			log.warn("getRotorFriction - intervalData为null！minIndex = {}， maxIndex = {}", minIndex, maxIndex);
			return 0;
		}
		double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
		Arrays.sort(intervalData);

		//获取Y3 及 其在原始频谱数组中的索引X3（下标）= 在srcIntervalData中的索引 + minIndex
		double maxValue = intervalData[intervalData.length - 1];
		int maxValueIndex = Doubles.indexOf(srcIntervalData, maxValue) + minIndex;
		//获取Y4（2*X3）、Y5（3*X3）、Y6（4*X3）、Y7（5*X3），并取Y3 - Y7的最小值Y8（maxValue）
		for (int i = 2; i < 6; i++) {
			if (maxValueIndex * i < frequencyWaveform.length
				&& BigDecimal.valueOf(maxValue).compareTo(BigDecimal.valueOf(frequencyWaveform[maxValueIndex * i])) > 0) {
				maxValue = frequencyWaveform[maxValueIndex * i];
			}
		}

		//获取Y8在频谱数组中的排名
		Arrays.sort(frequencyWaveform);
		return frequencyWaveform.length - Doubles.indexOf(frequencyWaveform, maxValue);
	}

	/**
	 * 计算 【频谱】1倍频 机理模型值。
	 * （1）计算转频
	 * 		X=转速/60，A×X=X1，B×X=X2，取X1、X2纵坐标最大值得出X3（横坐标的值）
	 * 		Y1为所有纵坐标值的均方根（频谱的均方根）
	 * （2）结果运用
	 * 		X对应的纵坐标为Y
	 * 		计算X3/Y1的值C
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param frequencyWaveform 频谱
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @return
	 */
	public static double getFreqDoubling(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] frequencyWaveform, String paramJson, double rotatingFreq) {
		//参数校验
		if (frequencyWaveform == null || frequencyWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).equals(BigDecimal.ZERO)) {
			log.warn("getFreqDoubling - 参数校验失败！");
			return 0;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取频谱数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		frequencyWaveform = Arrays.copyOf(frequencyWaveform, cutoffLength);

		//计算[x1, x2]
		BigDecimal freqStart = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal freqEnd = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));

		//校验频谱的最高评率是否大于X1
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(freqStart) <= 0) {
			log.info("getFreqDoubling - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围的数据。", cutoffFreq);
			return 0;
		}

		//获取[x1, x2]范围内的频谱幅值数据
		int minIndex = freqStart.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (freqEnd.compareTo(cutoffFreqKHz) < 0) {
			maxIndex = freqEnd.multiply(BigDecimal.valueOf(frequencyWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = frequencyWaveform.length;
		}

		//保存原始数据副本 & 排序（从小到大）
		double[] intervalData = Arrays.copyOfRange(frequencyWaveform, minIndex, maxIndex);
		if (intervalData.length <= 0) {
			log.warn("getFreqDoubling - intervalData为null！minIndex = {}， maxIndex = {}", minIndex, maxIndex);
			return 0;
		}
		double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
		Arrays.sort(intervalData);

		//获取Y3 及 其在原始频谱数组中的索引（下标）= 在srcIntervalData中的索引 + minIndex
		double maxValue = intervalData[intervalData.length - 1];
		int maxValueIndex = Doubles.indexOf(srcIntervalData, maxValue) + minIndex;

		//计算Y3对应的频率值X3（横坐标）
		BigDecimal maxValueFrequency = cutoffFreqKHz
			.divide(BigDecimal.valueOf(frequencyWaveform.length), 10, RoundingMode.HALF_UP)
			.multiply(BigDecimal.valueOf(maxValueIndex));

		//返回X3/Y1（频谱数组的均方根）
		return maxValueFrequency
			.divide(BigDecimal.valueOf(getRMS(frequencyWaveform)), 10, RoundingMode.HALF_UP)
			.doubleValue();
	}

	/**
	 * 获取【频谱】齿轮-齿轮磨损 机理模型值
	 * （1）计算转频
	 * 		X=转速/60，A×X=X1，B×X=X2，取 [X1、X2] 范围内的纵坐标最大值对应的横坐标X3（横坐标的值）
	 * （2）计算齿轮啮合频率
	 * 		X4=齿数×X3 （齿数为测点对应的多个齿数，循环遍历）
	 * （3）计算边频
	 * 		X5=(齿数-1）×X3
	 * 		X6=(齿数+1）×X3
	 * 		X7=(齿数-2）×X3
	 * 		X8=(齿数+2）×X3
	 * （4）结果运用
	 * 		X对应的纵坐标为Y
	 * 		计算（Y5+Y6+Y7+Y8）/Y4的值得出C（多个齿数算出多个C）
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param frequencyWaveform 频谱波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @param teethList 齿数集合（去重）
	 * @return
	 */
	public static List<BigDecimal> getGearWear(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] frequencyWaveform, String paramJson,
											   double rotatingFreq, List<Integer> teethList) {
		//参数校验
		if (frequencyWaveform == null || frequencyWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).compareTo(BigDecimal.ZERO) == 0 || CollectionUtil.isEmpty(teethList)) {
			log.warn("getGearWear - 参数校验失败！");
			return null;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取频谱数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		frequencyWaveform = Arrays.copyOf(frequencyWaveform, cutoffLength);

		//计算[x1, x2]
		BigDecimal freqStart = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal freqEnd = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));

		//校验频谱的最高评率是否大于X1
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(freqStart) <= 0) {
			log.info("getGearWear - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围的数据。", cutoffFreq);
			return null;
		}

		//获取[x1, x2]范围内的频谱幅值数据
		int minIndex = freqStart.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (freqEnd.compareTo(cutoffFreqKHz) < 0) {
			maxIndex = freqEnd.multiply(BigDecimal.valueOf(frequencyWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = frequencyWaveform.length;
		}

		//保存原始数据副本 & 排序（从小到大）
		double[] intervalData = Arrays.copyOfRange(frequencyWaveform, minIndex, maxIndex);
		double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
		Arrays.sort(intervalData);

		//获取Y3 及 其在原始频谱数组中的索引X3（下标）= 在srcIntervalData中的索引 + minIndex
		double maxValue = intervalData[intervalData.length - 1];
		int maxValueIndex = Doubles.indexOf(srcIntervalData, maxValue) + minIndex;

		//遍历齿数集合 - 求Y4 ~ Y8
		List<BigDecimal> result = new ArrayList<>();
		double[] finalFrequencyWaveform = frequencyWaveform;
		teethList.forEach(teethNumber -> {
			//校验最大变频是否超过截止频率
			if ((teethNumber + 2) * maxValueIndex >= finalFrequencyWaveform.length) {
				log.warn("getGearWear - 最大变频值（(齿数({})+2）* X3 = {}）超过了截止频率", teethNumber ,(teethNumber + 2) * maxValueIndex);
				result.add(BigDecimal.ZERO);
				return;
			}

			//求Y4 ~ Y8
			double y4 = finalFrequencyWaveform[teethNumber * maxValueIndex];
			double y5 = finalFrequencyWaveform[(teethNumber - 1) * maxValueIndex];
			double y6 = finalFrequencyWaveform[(teethNumber + 1) * maxValueIndex];
			double y7 = finalFrequencyWaveform[(teethNumber - 2) * maxValueIndex];
			double y8 = finalFrequencyWaveform[(teethNumber + 2) * maxValueIndex];

			//计算（Y5+Y6+Y7+Y8）/ Y4 的值
			result.add(BigDecimal.valueOf(y5)
				.add(BigDecimal.valueOf(y6))
				.add(BigDecimal.valueOf(y7))
				.add(BigDecimal.valueOf(y8))
				.divide(BigDecimal.valueOf(y4), 10, RoundingMode.HALF_UP));
		});

		return result;
	}

	/**
	 * 获取【频谱】齿轮-齿轮超负荷 机理模型值
	 * （1）计算转频
	 * 		X=转速/60，A×X=X1，B×X=X2，取X1、X2纵坐标最大值得出X3（横坐标的值）
	 * （2）计算齿轮啮合频率
	 * 		X4=齿数×X3
	 * （3）计算边频
	 * 		X5=(齿数-1）×X3
	 * 		X6=(齿数+1）×X3
	 * 		X7=(齿数-2）×X3
	 * 		X8=(齿数+2）×X3
	 * （4）结果运用
	 * 		X对应的纵坐标为Y
	 * 		若（Y5+Y6+Y7+Y8）/Y4＜1继续后续计算，计算Y4在所有数值（整个频谱的幅值）中的
	 * 		排名C（多个齿数算出多个C）
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param frequencyWaveform 频谱波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @param teethList 齿数集合（去重）
	 * @return
	 */
	public static List<BigDecimal> getGearOverload(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] frequencyWaveform, String paramJson,
												   double rotatingFreq, List<Integer> teethList) {
		//参数校验
		if (frequencyWaveform == null || frequencyWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).compareTo(BigDecimal.ZERO) == 0 || CollectionUtil.isEmpty(teethList)) {
			log.warn("getGearOverload - 参数校验失败！");
			return null;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取频谱数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		frequencyWaveform = Arrays.copyOf(frequencyWaveform, cutoffLength);

		//计算[x1, x2]
		BigDecimal freqStart = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal freqEnd = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));

		//校验频谱的最高评率是否大于X1
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(freqStart) <= 0) {
			log.info("getGearOverload - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围的数据。", cutoffFreq);
			return null;
		}

		//获取[x1, x2]范围内的频谱幅值数据
		int minIndex = freqStart.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (freqEnd.compareTo(cutoffFreqKHz) < 0) {
			maxIndex = freqEnd.multiply(BigDecimal.valueOf(frequencyWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = frequencyWaveform.length;
		}

		//保存原始数据副本 & 排序（从小到大）
		double[] intervalData = Arrays.copyOfRange(frequencyWaveform, minIndex, maxIndex);
		double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
		Arrays.sort(intervalData);

		//获取Y3 及 其在原始频谱数组中的索引X3（下标）= 在srcIntervalData中的索引 + minIndex
		double maxValue = intervalData[intervalData.length - 1];
		int maxValueIndex = Doubles.indexOf(srcIntervalData, maxValue) + minIndex;

		//遍历齿数集合 - 求Y4 ~ Y8
		List<BigDecimal> result = new ArrayList<>();
		double[] sortedFrequencyWaveform = Arrays.copyOf(frequencyWaveform, frequencyWaveform.length);
		Arrays.sort(sortedFrequencyWaveform);
		double[] finalFrequencyWaveform = frequencyWaveform;
		teethList.forEach(teethNumber -> {
			//校验最大边频是否超过截止频率
			if ((teethNumber + 2) * maxValueIndex >= finalFrequencyWaveform.length) {
				log.warn("getGearOverload - 最大边频值（(齿数({})+2）* X3 = {}）超过了截止频率", teethNumber ,(teethNumber + 2) * maxValueIndex);
				result.add(BigDecimal.ZERO);
				return;
			}

			//求Y4 ~ Y8
			double y4 = finalFrequencyWaveform[teethNumber * maxValueIndex];
			double y5 = finalFrequencyWaveform[(teethNumber - 1) * maxValueIndex];
			double y6 = finalFrequencyWaveform[(teethNumber + 1) * maxValueIndex];
			double y7 = finalFrequencyWaveform[(teethNumber - 2) * maxValueIndex];
			double y8 = finalFrequencyWaveform[(teethNumber + 2) * maxValueIndex];

			//若（Y5+Y6+Y7+Y8）/ Y4 ＜ 1继续后续计算（计算Y4在所有数值（整个频谱的幅值）中的排名），否则跳过当前齿轮。
			BigDecimal divide = BigDecimal.valueOf(y5)
				.add(BigDecimal.valueOf(y6))
				.add(BigDecimal.valueOf(y7))
				.add(BigDecimal.valueOf(y8))
				.divide(BigDecimal.valueOf(y4), 10, RoundingMode.HALF_UP);
			if (divide.compareTo(BigDecimal.ONE) < 0) {
				result.add(BigDecimal.valueOf(sortedFrequencyWaveform.length - Doubles.indexOf(sortedFrequencyWaveform, y4)));
			} else {
				log.warn("getGearOverload - (Y5 + Y6 + Y7 + Y8）/ Y4 >= 1，不满足机理模型计算条件，返回0！");
				result.add(BigDecimal.ZERO);
			}
		});

		return result;
	}

	/**
	 * 获取【频谱】齿轮-齿轮不正 机理模型值
	 * （1）计算转频
	 * 		X=转速/60，A×X=X1，B×X=X2，取X1、X2纵坐标最大值得出X3（横坐标的值）
	 * （2）计算齿轮啮合频率
	 * 		X4=齿数×X3
	 * 		X5=2×齿数×X3
	 * 		X6=3×齿数×X3
	 * （3）结果运用
	 * 		X对应的纵坐标为Y
	 * 		计算（Y5+Y6）/Y4的值C（多个齿数算出多个C）
	 *
	 * @param samplingFreq 采样频率（KHz）
	 * @param cutoffFreq 截止频率（Hz）
	 * @param frequencyWaveform 频谱波形数据
	 * @param paramJson 机理模型参数json字符串，有如下字段：{"amplitudeUpper":1.1,"amplitudeLower":0.9}
	 * @param rotatingFreq 转频
	 * @param teethList 齿数集合（去重）
	 * @return
	 */
	public static List<BigDecimal> getGearMisalign(BigDecimal samplingFreq, BigDecimal cutoffFreq, double[] frequencyWaveform, String paramJson, double rotatingFreq, List<Integer> teethList) {
		//参数校验
		if (frequencyWaveform == null || frequencyWaveform.length == 0 || StringUtil.isEmpty(paramJson)
			|| BigDecimal.valueOf(rotatingFreq).compareTo(BigDecimal.ZERO) == 0 || CollectionUtil.isEmpty(teethList)) {
			log.warn("getGearMisalign - 参数校验失败！");
			return null;
		}

		//获取机理模型的参数
		JSONObject paramObject = JSONObject.parseObject(paramJson);
		double amplitudeLower = paramObject.getDoubleValue("amplitudeLower");
		double amplitudeUpper = paramObject.getDoubleValue("amplitudeUpper");

		//根据截止频率截取频谱数据
		BigDecimal envelopWaveFormFreqMax = samplingFreq.multiply(BigDecimal.valueOf(1000))
			.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
		int cutoffLength = cutoffFreq.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(envelopWaveFormFreqMax, 2, RoundingMode.HALF_UP)
			.intValue();
		frequencyWaveform = Arrays.copyOf(frequencyWaveform, cutoffLength);

		//计算[x1, x2]
		BigDecimal freqStart = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeLower));
		BigDecimal freqEnd = BigDecimal.valueOf(rotatingFreq).multiply(BigDecimal.valueOf(amplitudeUpper));

		//校验频谱的最高评率是否大于X1
		BigDecimal cutoffFreqKHz = cutoffFreq.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
		if (cutoffFreqKHz.compareTo(freqStart) <= 0) {
			log.info("getGearMisalign - 当前波形的截止频率(cutoffFreq = {})过低，无指定频率范围的数据。", cutoffFreq);
			return null;
		}

		//获取[x1, x2]范围内的频谱幅值数据
		int minIndex = freqStart.multiply(BigDecimal.valueOf(frequencyWaveform.length))
			.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
			.intValue();
		int maxIndex;
		if (freqEnd.compareTo(cutoffFreqKHz) < 0) {
			maxIndex = freqEnd.multiply(BigDecimal.valueOf(frequencyWaveform.length))
				.divide(cutoffFreqKHz, 10, RoundingMode.HALF_UP)
				.intValue();
		} else {
			maxIndex = frequencyWaveform.length;
		}

		//保存原始数据副本 & 排序（从小到大）
		double[] intervalData = Arrays.copyOfRange(frequencyWaveform, minIndex, maxIndex);
		double[] srcIntervalData = Arrays.copyOf(intervalData, intervalData.length);
		Arrays.sort(intervalData);

		//获取Y3 及 其在原始频谱数组中的索引X3（下标）= 在srcIntervalData中的索引 + minIndex
		double maxValue = intervalData[intervalData.length - 1];
		int maxValueIndex = Doubles.indexOf(srcIntervalData, maxValue) + minIndex;

		//遍历齿数集合 - 求Y4 ~ Y6
		List<BigDecimal> result = new ArrayList<>();
		double[] finalFrequencyWaveform = frequencyWaveform;
		teethList.forEach(teethNumber -> {
			//校验最大边频是否超过截止频率
			if (3 * teethNumber * maxValueIndex >= finalFrequencyWaveform.length) {
				log.warn("getGearMisalign - 最大边频值（3 × 齿数({}) × X3）= {} 超过了截止频率！", teethNumber, 3 * teethNumber * maxValueIndex);
				result.add(BigDecimal.ZERO);
				return;
			}

			//求Y4 ~ Y6
			double y4 = finalFrequencyWaveform[teethNumber * maxValueIndex];
			double y5 = finalFrequencyWaveform[2 * teethNumber * maxValueIndex];
			double y6 = finalFrequencyWaveform[3 * teethNumber * maxValueIndex];

			//计算（Y5+Y6）/ Y4 的值
			result.add(BigDecimal.valueOf(y5)
				.add(BigDecimal.valueOf(y6))
				.divide(BigDecimal.valueOf(y4), 10, RoundingMode.HALF_UP));
		});

		return result;
	}

	/**
	 * 通过积分计算振动数据的速度数据
	 * @param data 振动数据
	 * @param samplingTime 采样时长，单位S。
	 * @return
	 */
	public static double[] calculateVelocity(double[] data, BigDecimal samplingTime) {
		// 参数校验
		if (data == null || data.length == 0 || samplingTime.compareTo(BigDecimal.ZERO) <= 0) {
			log.warn("calculateVelocity - 参数校验失败！");
			return new double[0];
		}

		// 将振动数据的单位 m/s^2 换算为 mm/s^2
		for (int i = 0; i < data.length; i++) {
			data[i] = data[i] * 1000;
		}

		// 初始化速度数组，初始速度为0
		double[] velocities = new double[data.length];
		velocities[0] = 0.0;

		// 采集点时间间隔，采集时长 / 采集数据长度
		double timeInterval = samplingTime.divide(BigDecimal.valueOf(data.length), 6, RoundingMode.HALF_UP).doubleValue();

		// 通过使用矩形规则进行数值积分，计算每个时间点的速度增量，并将其累加到前一个速度值上，得到速度数据数组。
		for (int i = 1; i < data.length; i++) {
			double velocityIncrement = (data[i] + data[i - 1]) / 2.0 * timeInterval;
			velocities[i] = velocities[i - 1] + velocityIncrement;
		}

		return velocities;
	}

	/*public static void main(String[] args) {
		double[] data = {0.10436457085828342, 0.625375249500998, 0.12304461077844311, -0.583142115768463, 0.00934001996007984, 0.6602988023952095, -0.14822205588822354, -0.9867934131736527, -0.014619161676646708, 1.066386626746507, 0.17786646706586826, -0.9234437125748503, -0.06944101796407186, 1.1849642714570858, 0.31553023952095804, -1.1829338323353293, -0.6432431137724551, 0.9961334331337326, 0.8458809381237524, -0.7402981037924152, -0.869840119760479, 0.5778629740518962, 0.8974540918163673, -0.5498429141716566, -1.2422226546906188, 0.25989620758483034, 1.496433632734531, 0.15187684630738524, -1.2544052894211577, -0.10517674650698602, 1.3896325349301397, 0.4190826347305389, -1.253593113772455, -0.7199937125748502, 0.954306387225549, 0.7500442115768463, -0.7845616766467066, -0.8024295409181638, 0.595324750499002, 0.8576574850299402, -0.46943752495009977, -1.0700414171656685, -0.024365269461077844, 0.9478089820359281, 0.33421027944111775, -0.6387761477045908, -0.27776407185628743, 0.6269996007984031, 0.33380419161676644, -0.5612133732534931, -0.34314421157684627, 0.4186765469061876, 0.29400758483033934, -0.3411137724550898, -0.22537874251497006, 0.3309615768463074, 0.2720788423153693, -0.24081007984031935, -0.33502245508982037, 0.05725838323353293, 0.15715598802395211, -0.17177514970059882, -0.2814188622754491, 0.1746177644710579, 0.5246654690618763, 0.08121756487025948, -0.5790812375249501, -0.3630425149700599, 0.5035489021956088, 0.48283842315369263, -0.3719764471057884, -0.5608072854291417, 0.1421307385229541, 0.5916699600798403, 0.08852714570858283, -0.4592853293413174, -0.22700309381237524, 0.27370319361277445, 0.361824251497006, 0.004060878243512974, -0.33421027944111775, -0.16081077844311378, 0.42436177644710577, 0.4661888223552894, -0.2765458083832335, -0.7358311377245509, -0.12832375249501, 0.7561355289421158, 0.4540061876247505, -0.46456447105788423, -0.46903143712574846, 0.36994600798403193, 0.6927858283433134, -0.005279141716566866, -0.6493344311377245, -0.28710409181636726, 0.6667962075848304, 0.6310604790419161, -0.39065648702594813, -0.6996893213572855, 0.1689325349301397, 0.6960345309381237, -0.09949151696606787, -0.6720753493013971, -0.06334970059880239, 0.4617218562874251, 0.11532894211576847, -0.17908473053892215, 0.09096367265469062, 0.16324730538922155, -0.15512554890219563, 0.001218263473053892, 0.4621279441117765, 0.10152195608782434, -0.6724814371257486, -0.47796536926147704, 0.6988771457085828, 0.8064904191616766, -0.5713655688622754, -1.1752181636726549, 0.3740068862275449, 1.7210001996007984, 0.19410998003992017, -1.8984605788423152, -1.1232389221556887, 1.647092215568862, 1.5926764471057884, -1.3002932135728544, -2.0365304391217567, 0.8081147704590819, 2.455206986027944, -0.20832305389221556, -2.4511461077844308, -0.9534942115768462, 1.8034360279441117, 1.425774351297405, -1.160599001996008, -1.8290195608782436, 0.14253682634730538, 1.9715563872255488, 0.6749179640718562, -1.3518663672654692, -0.8755253493013971, 1.1191780439121757, 1.3547089820359282, -0.4406052894211577, -1.3534907185628744, 0.017867864271457083, 1.4517639720558881, 0.35289031936127746, -1.2962323353293412, -1.0310569860279442, 0.554309880239521, 1.0639500998003992, -0.17989690618762474, -1.0923762475049899, -0.36832165668662675, 1.0927823353293413, 0.8304496007984031, -0.8272008982035928, -0.9628342315369262, 0.7630390219560879, 1.3283132734530938, -0.2732971057884232, -1.1118684630738522, 0.15228293413173652, 1.3262828343313373, 0.32162155688622757, -1.1516650698602793, -0.9575550898203593, 0.5457820359281437, 1.169939021956088, -0.11248632734530938, -1.093594510978044, -0.28710409181636726, 1.059483133732535, 0.6306543912175648, -1.0042551896207585, -1.1346093812375249, 0.749638123752495, 1.548412874251497, -0.2944136726546906, -1.5817120758483034, -0.2651753493013972, 1.4944031936127744, 0.7459833333333333, -1.0209047904191617, -0.9608037924151697, 0.6160352295409182, 1.2966384231536927, 0.040202694610778446, -1.0330874251497004, -0.39796606786427147, 0.9660829341317365, 0.8162365269461077, -0.7143084830339321, -1.1983651696606787, 0.19614041916167665, 1.3555211576846307, 0.3220276447105788, -1.0732901197604792, -0.7971503992015968, 0.3585755489021956, 0.6140047904191617, -0.25583532934131736, -0.7398920159680639, -0.21888133732534928, 0.6911614770459082, 0.9059819361277446, 0.024365269461077844, -0.7476076846307386, -0.2501500998003992, 0.7228363273453093, 0.3744129740518962, -0.7293337325349302, -0.756541616766467, 0.32365199600798406, 0.810957385229541, -0.124262874251497, -0.8336983033932136, -0.3159363273453094, 0.4856810379241517, 0.3760373253493014, -0.3557329341317365, -0.40405738522954093, 0.28913453093812375, 0.6538013972055888, 0.11370459081836327, -0.43329570858283434, -0.32771287425149703, -0.041014870259481036, 0.2192874251497006, 0.42720439121756487, 0.22740918163672655, -0.28913453093812375, -0.07715668662674652, 0.5567464071856287, 0.07390798403193613, -0.9632403193612774, -0.6156291417165668, 0.7484198602794411, 0.6968467065868263, -0.5441576846307385, -0.4105547904191617, 0.9315654690618763, 0.9161341317365269, -0.6598927145708583, -1.188212974051896, 0.3557329341317365, 1.1118684630738522, -0.42964091816367267, -1.4728805389221558, 0.33705289421157686, 1.9439424151696605, 0.04263922155688622, -2.1193723552894213, -0.524259381237525, 2.363431137724551, 1.115523253493014, -2.0296269461077845, -1.5423215568862274, 1.7246549900199601, 2.0722661676646705, -0.965270758483034, -1.9821146706586827, 0.312687624750499, 1.9683076846307386, 0.17949081836327346, -1.784755988023952, -0.9388750499001995, 1.3733890219560878, 1.3551150698602794, -1.103340618762475, -1.8103395209580837, 0.32243373253493013, 1.780695109780439, 0.1254811377245509, -1.3100393213572854, -0.0950245508982036, 1.1731877245508981, 0.2489318363273453, -0.8158304391217565, -0.24040399201596807, 0.4207069860279441, 0.09543063872255489, -0.25258662674650695, -0.02030439121756487, 0.13319680638722556, 0.14740988023952095, 0.13035419161676648, -0.13806986027944113, -0.30862674650698607, -0.03736007984031936, 0.16162295409181637, -0.08609061876247505, -0.04142095808383234, 0.4052756487025948, 0.21482045908183633, -0.4239556886227545, -0.24405878243512974, 0.3666973053892216, 0.11126806387225549, -0.39999650698602796, -0.14781596806387226, 0.21279001996007985, 0.001218263473053892, -0.15431337325349304, 0.12913592814371255, 0.14253682634730538, -0.07593842315369262, 0.06456796407185628, 0.1104558882235529, -0.13603942115768464, 0.07756277445109781, 0.5494368263473054, 0.22537874251497006, -0.46009750499001995, -0.33908333333333335, 0.22131786427145708, 0.21441437125748505, -0.15471946107784432, -0.10152195608782434, 0.1701507984031936, 0.22253612774451098, 0.0471061876247505, -0.26476926147704594, -0.29197714570858285, 0.09015149700598803, 0.24324660678642715, -0.11979590818363274, -0.2464953093812375, 0.1912673652694611, 0.3394894211576846, -0.13238463073852297, -0.43085918163672654, -0.11289241516966068, 0.16608992015968063, -0.03289311377245509, -0.15918642714570858, 0.13197854291417166, 0.3622303393213573, 0.16568383233532935, -0.14984640718562875, -0.15878033932135727, 0.13157245508982035, 0.11492285429141716, -0.28954061876247505, -0.0917758483033932, 0.6432431137724551, 0.4373565868263473, -0.5900456087824351, -0.5201985029940119, 0.640400499001996, 0.689537125748503, -0.7037501996007984, -1.2665879241516966, 0.25989620758483034, 1.4481091816367264, 0.03329920159680639, -1.6093260479041915, -0.47796536926147704, 1.607295608782435, 0.8653731536926148, -1.3096332335329341, -1.3701403193612776, 0.6594866267465069, 1.6775488023952094, 0.051573153692614775, -1.5670929141716567, -0.8596879241516966, 1.277958383233533, 1.3827290419161677, -0.7419224550898204, -1.245471357285429, 0.6071012974051896, 1.480190119760479, -0.0471061876247505, -1.1492285429141715, -0.33461636726546906, 0.9088245508982036, 0.6956284431137725, -0.4848688622754491, -0.7780642714570858, 0.04466966067864271, 0.817860878243513, 0.33908333333333335, -0.7053745508982036, -0.7951199600798403, 0.19410998003992017, 0.7159328343313373, -0.07756277445109781, -0.7297398203592814, -0.0312687624750499, 0.7874042914171657, 0.19451606786427145, -0.6838518962075849, -0.39715389221556885, 0.4621279441117765, 0.6241569860279441, -0.02842614770459082, -0.5644620758483033, -0.28994670658682636, 0.4401992015968064, 0.44913313373253494, -0.33177375249500995, -0.678572754491018, 0.10355239520958084, 0.8048660678642714, 0.13806986027944113, -0.7614146706586826, -0.24690139720558882, 0.8450687624750499, 0.5145132734530938, -0.6899432135728543, -0.7411102794411177, 0.37644341317365265, 0.9092306387225548, 0.14944031936127744, -0.6554257485029941, -0.43451397205588826, 0.4097426147704591, 0.5534977045908183, -0.29197714570858285, -0.7476076846307386, 0.018273952095808385, 0.8868958083832335, 0.3240580838323353, -0.7264911177644711, -0.4458844311377246, 0.47431057884231537, 0.49380279441117764, -0.24162225548902197, -0.5218228542914172, -0.08690279441117765, 0.30009890219560875, 0.34314421157684627, 0.08730888223552895, -0.28994670658682636, -0.3114693612774451, 0.17786646706586826, 0.44750878243512976, -0.07472015968063872, -0.5108584830339321, -0.010152195608782435, 0.6001978043912175, 0.27410928143712576, -0.37928602794411176, -0.40690000000000004, 0.11776546906187624, 0.39877824351297403, -0.0008121756487025948, -0.35370249500998, -0.13888203592814372, 0.20588652694610776, 0.16690209580838322, -0.1275115768463074, -0.2018256487025948, -0.0008121756487025948, 0.22659700598802396, 0.2205056886227545, -0.04263922155688622, -0.44629051896207583, -0.41136696606786427, 0.29238323353293416, 0.5340054890219561, -0.18476996007984034, -0.51329500998004, 0.40283912175648706, 0.8186730538922156, -0.24446487025948105, -0.7362372255489023, 0.16284121756487027, 0.42679830339321356, -0.4186765469061876, -0.4235496007984032, 0.5608072854291417, 0.6822275449101796, -0.3029415169660679, -0.6883188622754491, 0.35695119760479044, 0.9482150698602795, -0.16040469061876247, -1.3717646706586826, -0.25949011976047903, 1.7445532934131738, 0.5798934131736527, -2.0742966067864272, -1.354302894211577, 2.143331536926148, 1.980084231536926, -1.6933862275449103, -2.3366293413173653, 1.3145062874251496, 2.94535499001996, -0.3894382235528942, -2.6034290419161676, -0.5973551896207584, 2.3561215568862277, 1.2028321357285428, -1.858257884231537, -2.0239417165668665, 0.9478089820359281, 2.804848602794411, 0.08893323353293413, -2.4247503992015966, -0.9717681636726546, 2.055210479041916, 1.40222125748503, -1.5727781437125747, -1.6324730538922154, 0.753699001996008, 1.650747005988024, -0.02355309381237525, -0.9981638722554891, -0.2615205588822355, 0.5612133732534931, 0.34395638722554894, -0.34314421157684627, -0.584360379241517, -0.0917758483033932, 0.5246654690618763, 0.2651753493013972, -0.36994600798403193, -0.5149193612774451, -0.15147075848303396, 0.24284051896207584, 0.3963417165668663, 0.20994740518962077, -0.18476996007984034, -0.28751017964071857, 0.28588582834331333, 0.6627353293413174, -0.027613972055888225, -0.7488259481037924, -0.27776407185628743, 0.6367457085828343, 0.32974331337325347, -0.5660864271457086, -0.40486956087824355, 0.486087125748503, 0.640400499001996, -0.1441611776447106, -0.6846640718562874, -0.3447685628742515, 0.4588792415169661, 0.6428370259481039, -0.0467000998003992, -0.6079134730538922, -0.22375439121756488, 0.4954271457085829, 0.2952258483033932, -0.4755288423153693, -0.4824323353293413, 0.3825347305389222, 0.8040538922155689, 0.16040469061876247, -0.5608072854291417, -0.2927893213572854, 0.41461566866267463, 0.33421027944111775, -0.2793884231536926, -0.4093365269461078, 0.1880186626746507, 0.6001978043912175, 0.19573433133732535, -0.4320774451097804, -0.43126526946107785, 0.15268902195608783, 0.20994740518962077, -0.36994600798403193, -0.37644341317365265, 0.31674850299401197, 0.4389809381237525, -0.19735868263473053, -0.4032452095808383, 0.16243512974051896, 0.50395499001996, -0.010152195608782435, -0.5644620758483033, -0.40162085828343314, 0.50395499001996, 0.9876055888223553, 0.007715668662674651, -1.169939021956088, -0.4864932135728543, 1.3575515968063872, 0.8925810379241517, -1.4700379241516965, -1.720594111776447, 1.1508528942115768, 2.2578483033932133, -0.7309580838323353, -2.4324660678642713, 0.23147005988023953, 2.6614996007984035, 0.45116357285429143, -2.173788123752495, -1.302323652694611, 1.3746072854291418, 1.9804903193612773, -0.42842265469061874, -2.203838622754491, -0.8901445109780439, 2.02191127744511, 1.4172465069860278, -1.5930825349301396, -1.342120259481038, 1.3936934131736527, 1.5947068862275449, -0.7078110778443114, -1.256029640718563, 0.1872064870259481, 1.0066917165668663, 0.19654650698602794, -0.6302483033932136, -0.5640559880239521, 0.08324800399201597, 0.8292313373253493, 0.5335994011976047, -0.6209082834331338, -0.806896506986028, 0.3374589820359281, 0.6911614770459082, -0.32365199600798406, -0.6249691616766467, 0.42720439121756487, 0.8491296407185629, -0.06050708582834331, -0.6424309381237524, -0.13116636726546907, 0.5384724550898203, 0.5055793413173654, -0.19614041916167665, -0.8377591816367265, -0.3496416167664671, 0.7260850299401198, 0.5076097804391217, -0.6931919161676646, -0.752480738522954, 0.5400968063872256, 0.918976746506986, -0.30943892215568863, -0.9526820359281437, 0.18192734530938123, 1.1411067864271458, 0.20588652694610776, -1.064762275449102, -0.7232424151696607, 0.6915675648702595, 1.001006487025948, -0.1599986027944112, -0.858875748502994, -0.22578483033932137, 0.7009075848303393, 0.44953922155688625, -0.5522794411177645, -0.7029380239520958, 0.1579681636726547, 0.6424309381237524, 0.008527844311377245, -0.5384724550898203, -0.21075958083832336, 0.4385748502994012, 0.6010099800399201, -0.035735728542914166, -0.749638123752495, -0.5782690618762475, 0.45116357285429143, 0.78253123752495, -0.19735868263473053, -0.8763375249500999, -0.09827325349301397, 0.9539002994011977, 0.49136626746506984, -0.6862884231536925, -0.6980649700598802, 0.38212864271457087, 0.757759880239521, -0.16608992015968063, -0.7711607784431137, -0.05319750499001996, 0.7528868263473053, 0.24608922155688623, -0.5928882235528942, -0.30212934131736524, 0.4722801397205589, 0.3151241516966068, -0.3061902195608782, -0.14944031936127744, 0.3082206586826347, 0.13238463073852297, -0.18476996007984034, 0.16081077844311378, 0.527101996007984, -0.010152195608782435, -0.6225326347305389, 0.02355309381237525, 0.8012112774451098, 0.04873053892215569, -0.8990784431137725, -0.1721812375249501, 0.8702462075848303, 0.16933862275449102, -0.8791801397205589, -0.3496416167664671, 0.5928882235528942, 0.3354285429141716, -0.4337017964071856, -0.3561390219560878, 0.40811826347305385, 0.5705533932135729, -0.2879162674650698, -0.5876090818363274, 0.49014800399201597, 0.8613122754491018, -0.4767471057884231, -0.9945090818363274, 0.7029380239520958, 1.346181137724551, -0.6468979041916169, -1.5329815369261477, 0.5636499001996008, 1.8005934131736527, -0.22334830339321357, -1.687700998003992, -0.12345069860279441, 1.4606979041916168, 0.39471736526946105, -1.2202939121756489, -0.9031393213572855, 0.6209082834331338, 0.9701438123752495, -0.44750878243512976, -1.3457750499001997, -0.3601999001996008, 1.201613872255489, 0.8040538922155689, -0.8978601796407185, -1.0728840319361277, 0.49948802395209585, 1.1053710578842315, -0.15228293413173652, -0.7780642714570858, -0.10070978043912177, 0.4633462075848303, 0.4142095808383234, 0.2611144710578842, -0.00934001996007984, -0.3744129740518962, -0.06538013972055888, 0.565274251497006, 0.09746107784431138, -0.8284191616766468, -0.4856810379241517, 0.4836505988023952, 0.51329500998004, -0.3289311377245509, -0.756541616766467, -0.17746037924151697, 0.7666938123752495, 0.6899432135728543, -0.391874750499002, -0.8759314371257485, 0.06334970059880239, 1.0810057884231536, 0.31025109780439125, -0.9876055888223553, -0.6420248502994012, 0.8361348303393213, 0.9583672654690619, -0.42964091816367267, -0.8568453093812375, 0.237561377245509, 1.1029345309381238, 0.3597938123752495, -0.8755253493013971, -0.9677072854291416, 0.3212154690618762, 1.1638477045908184, 0.07025319361277445, -1.0834423153692614, -0.3626364271457086, 1.0261839321357287, 0.6619231536926148, -0.5928882235528942, -0.6712631736526946, 0.1912673652694611, 0.6944101796407185, 0.2367492015968064, -0.421925249500998, -0.41705219560878243, 0.28710409181636726, 0.6131926147704592, -0.027207884231536927, -0.6842579840319362, -0.3293372255489022, 0.5599951097804391, 0.6229387225548902, -0.20548043912175648, -0.6148169660678643, -0.024771357285429143, 0.38821996007984033, -0.06741057884231537, -0.5088280439121756, -0.20669870259481038, 0.395935628742515, 0.5717716566866267, 0.09664890219560879, -0.4077121756487026, -0.30862674650698607, 0.044263572854291416, 0.3488294411177644, 0.23918572854291417, -0.2972562874251497, -0.4734984031936128, 0.376849500998004, 1.1049649700598803, 0.13076027944111776, -1.4180586826347303, -1.0464883233532933, 1.06110748502994, 1.1630355289421157, -1.1216145708582834, -1.6990714570858283, 0.9494333333333332, 2.1794733532934134, -0.44913313373253494, -2.284650099800399, -0.5644620758483033, 1.9216075848303393, 1.0655744510978045, -1.3973482035928144, -1.381510778443114, 0.908012375249501, 1.7904412175648703, -0.2672057884231537, -1.5934886227544909, -0.11289241516966068, 1.4980579840319361, 0.4499453093812375, -1.0667927145708582, -0.4215191616766467, 0.9185706586826348, 0.7488259481037924, -0.49217844311377246, -0.689537125748503, 0.05807055888223553, 0.19817085828343314, -0.09624281437125749, 0.05685229540918164, 0.22375439121756488, -0.009746107784431137, -0.13644550898203592, 0.03857834331337326, 0.057664471057884234, -0.16730818363273453, -0.2160387225548902, 0.14253682634730538, 0.41014870259481034, 0.11208023952095808, -0.31674850299401197, -0.13522724550898205, 0.4844627744510978, 0.33380419161676644, -0.5368481037924152, -0.5470002994011977, 0.4422296407185629, 0.711465868263473, -0.14578552894211574, -0.6464918163672655, -0.04994880239520958, 0.48771147704590817, -0.03857834331337326, -0.5884212574850299, -0.15025249500998003, 0.607507385229541, 0.5319750499001996, -0.2062926147704591, -0.46131576846307387, 0.0779688622754491, 0.51329500998004, 0.05482185628742515, -0.5636499001996008, -0.33461636726546906, 0.44426007984031934, 0.5137010978043912, -0.22619091816367265, -0.45116357285429143, 0.10355239520958084, 0.43085918163672654, 0.0467000998003992, -0.346799001996008, -0.19938912175648701, 0.2655814371257485, 0.5084219560878244, 0.09867934131736526, -0.5096402195608782, -0.48121407185628745, 0.17583602794411177, 0.5047671656686626, 0.012994810379241517, -0.4288287425149701, -0.0787810379241517, 0.47309231536926144, 0.2988806387225549, -0.37522514970059884, -0.6497405189620759, -0.19695259481037924, 0.38375299401197605, 0.27532754491017963, -0.2915710578842315, -0.3037536926147705, 0.37238253493013973, 0.5214167664670659, -0.2038560878243513, -0.5847664670658683, -0.12913592814371255, 0.20548043912175648, 0.006091317365269461, -0.005279141716566866, 0.15674990019960078, 0.03939051896207585, -0.01258872255489022, 0.20507435129740517, 0.12304461077844311, -0.5372541916167665, -0.6542074850299402, 0.5254776447105789, 1.0850666666666666, -0.16812035928143715, -0.9920725548902196, 0.20101347305389222, 1.2263852295409183, 0.09380628742514971, -1.2592783433133732, -0.6310604790419161, 0.8503479041916168, 0.8121756487025947, -0.49339670658682633, -0.8255765469061876, 0.3841590818363273, 1.0732901197604792, -0.109237624750499, -1.197552994011976, -0.30050499001996006, 1.0870971057884231, 0.6229387225548902, -0.6006038922155689, -0.48121407185628745, 0.28954061876247505, 0.31553023952095804, -0.043451397205588826, 0.07837495009980039, 0.23025179640718563, -0.04182704590818363, -0.03248702594810379, 0.39959041916167665, 0.24040399201596807, -0.31553023952095804, -0.2680179640718563, 0.09055758483033932, 0.13279071856287425, -0.13035419161676648, -0.33421027944111775, -0.12060808383233533, 0.32527634730538924, 0.22862744510978045, -0.3898443113772455, -0.46131576846307387, 0.1721812375249501, 0.4211130738522954, -0.0950245508982036, -0.2501500998003992, 0.19979520958083832, 0.19167345309381237, -0.22903353293413173, -0.06822275449101796, 0.5291324351297405, 0.29197714570858285, -0.5238532934131735, -0.5197924151696607, 0.31634241516966066, 0.50395499001996, -0.30943892215568863, -0.7829373253493014, -0.18761257485029942, 0.6818214570858283, 0.25542924151696605, -0.9222254491017964, -0.6980649700598802, 0.9112610778443113, 1.1208023952095807, -0.5604011976047903, -1.3279071856287425, 0.12913592814371255, 1.6592748502994012, 0.6054769461077844, -1.1691268463073852, -0.9201950099800399, 0.8657792415169661, 1.2434409181636727, -0.5717716566866267, -1.4338961077844312, -0.002436526946107784, 1.4972458083832334, 0.6160352295409182, -1.1800912175648701, -1.0927823353293413, 0.7666938123752495, 1.4375508982035927, -0.23147005988023953, -1.4367387225548902, -0.4885236526946108, 1.1313606786427146, 0.892987125748503, -0.6330909181636727, -0.9007027944111776, 0.3362407185628743, 1.0720718562874252, 0.19167345309381237, -0.7699425149700599, -0.43085918163672654, 0.5555281437125749, 0.6875066866267465, -0.109237624750499, -0.6574561876247506, -0.2330944111776447, 0.6196900199600798, 0.5957308383233533, -0.2980684630738523, -0.6079134730538922, 0.06010099800399202, 0.4767471057884231, 0.021522654690618762, -0.44507225548902196, -0.25949011976047903, 0.07309580838323354, 0.16324730538922155, 0.0032487025948103793, -0.16243512974051896, 0.08527844311377245, 0.2838553892215569, -0.05928882235528942, -0.25867794411177647, 0.19695259481037924, 0.49217844311377246, -0.15837425149700599, -0.6164413173652694, 0.20832305389221556, 0.7411102794411177, -0.29197714570858285, -1.0424274451097806, 0.0629436127744511, 1.0038491017964073, -0.03532964071856288, -1.0757266467065867, 0.055634031936127745, 1.2365374251497006, 0.046294011976047905, -1.2946079840319362, -0.3443624750499002, 0.9392811377245509, 0.2176630738522954, -0.9364385229540918, -0.3260885229540918, 1.0651683632734532, 0.7203998003992016, -0.9226315369261477, -1.0217169660678642, 0.8840531936127745, 1.2913592814371258, -0.8381652694610778, -1.531763273453094, 0.5798934131736527, 1.710848003992016, -0.11208023952095808, -1.465164870259481, -0.02680179640718563, 1.5065858283433133, 0.3788799401197605, -1.299887125748503, -0.7411102794411177, 1.052985728542914, 1.1593807385229542, -0.5900456087824351, -1.2544052894211577, 0.1405063872255489, 1.2255730538922154, 0.24081007984031935, -0.8893323353293413, -0.2655814371257485, 0.8097391217564871, 0.4077121756487026, -0.5364420159680638, -0.3114693612774451, 0.26273882235528945, 0.02355309381237525, -0.39552954091816367, -0.07472015968063872, 0.3313676646706587, 0.16202904191616765, -0.06538013972055888, -0.034923552894211576, 0.03736007984031936, -0.061725349301397205, -0.312687624750499, -0.3561390219560878, 0.21157175648702595, 0.7110597804391218, 0.1563438123752495, -0.5782690618762475, -0.06091317365269461, 0.8097391217564871, 0.3642607784431138, -0.5746142714570858, -0.4734984031936128, 0.19329780439121758, 0.32365199600798406, 0.08040538922155689, -0.09015149700598803, -0.19654650698602794, -0.018273952095808385, 0.3476111776447106, 0.13116636726546907, -0.5656803393213573, -0.4527879241516966, 0.5275080838323353, 0.6822275449101796, -0.376849500998004, -0.9307532934131736, -0.11329850299401198, 0.752480738522954, 0.3309615768463074, -0.5640559880239521, -0.524259381237525, 0.3573572854291417, 0.7179632734530939, -0.11735938123752494, -0.9051697604790419, -0.31431197604790423, 0.7151206586826347, 0.36832165668662675, -0.6119743512974052, -0.372788622754491, 0.7025319361277446, 0.7159328343313373, -0.3098450099800399, -0.5360359281437126, 0.12385678642714572, 0.23593702594810378, -0.19735868263473053, -0.10314630738522955, 0.28872844311377244, 0.13766377245508982, -0.2022317365269461, 0.10274021956087824, 0.36832165668662675, -0.2704544910179641, -0.731770259481038, 0.06984710578842315, 0.8544087824351297, 0.13644550898203592, -0.8133939121756487, -0.1405063872255489, 1.063544011976048, 0.47593493013972055, -1.093594510978044, -0.9916664670658683, 0.8336983033932136, 1.2235426147704591, -0.531162874251497, -1.2921714570858283, 0.361824251497006, 1.5800877245508982, 0.019492215568862273, -1.5776511976047904, -0.5498429141716566, 1.3672977045908186, 0.9949151696606787, -0.8081147704590819, -0.9932908183632734, 0.45156966067864274, 1.099279740518962, -0.022334830339321356, -0.8958297405189621, -0.26679970059880237, 0.741516367265469, 0.6241569860279441, -0.34192594810379245, -0.7512624750499002, 0.0312687624750499, 0.7374554890219561, 0.06578622754491018, -0.7999930139720559, -0.5327872255489022, 0.391874750499002, 0.6716692614770459, 0.08609061876247505, -0.49136626746506984, -0.3934991017964072, 0.2964441117764471, 0.41542784431137725, -0.3484233532934132, -0.6647657684630738, 0.2351248502994012, 0.9474028942115769, 0.17177514970059882, -0.7041562874251497, -0.23837355289421155, 0.6643596806387225, 0.387813872255489, -0.6001978043912175, -0.7500442115768463, 0.25136836327345313, 0.9717681636726546, 0.25258662674650695, -0.7143084830339321, -0.4816201596806387, 0.5413150698602794, 0.5100463073852296, -0.6728875249500998, -1.0018186626746506, 0.36385469061876247, 1.275521856287425, 0.036953992015968065, -1.1991773453093812, -0.451975748502994, 1.0209047904191617, 0.7894347305389221, -0.6481161676646706, -0.9132915169660679, 0.3374589820359281, 1.1504468063872255, 0.15837425149700599, -1.006285628742515, -0.5595890219560878, 0.7329885229540918, 0.7963382235528942, -0.40608782435129737, -0.9116671656686627, 0.08852714570858283, 1.0858788423153694, 0.43126526946107785, -0.8414139720558882, -0.8406017964071857, 0.2209117764471058, 0.6428370259481039, -0.00934001996007984, -0.464158383233533, -0.0637557884231537, 0.508015868263473, 0.5031428143712575, -0.10598892215568863, -0.6322787425149701, -0.3220276447105788, 0.43288962075848303, 0.41339740518962076, -0.2363431137724551, -0.3151241516966068, 0.2196935129740519, 0.4661888223552894, 0.05279141716566866, -0.3756312375249501, -0.2846675648702595, 0.07472015968063872, 0.2814188622754491, 0.07634451097804391, -0.2834493013972056, -0.143349001996008, 0.31553023952095804, 0.27126666666666666, -0.19410998003992017, -0.37319471057884235, -0.23350049900199601, 0.01868003992015968, 0.46781317365269465, 0.5262898203592814, -0.2205056886227545, -0.5372541916167665, 0.3934991017964072, 0.8048660678642714, -0.43613832335329344, -1.0428335329341318, 0.27126666666666666, 0.950245508982036, -0.25258662674650695, -0.9746107784431137, 0.27532754491017963, 1.260090518962076, -0.027613972055888225, -1.3733890219560878, -0.19207954091816365, 1.3595820359281439, 0.2948197604790419, -1.5285145708582835, -0.7139023952095809, 1.5232354291417167, 0.9807020958083833, -1.517144111776447, -1.5536920159680638, 1.5419154690618762, 2.411349500998004, -0.9055758483033932, -2.4986583832335327, 0.42801656686626743, 2.506780139720559, -0.02964441117764471, -2.327695409181637, -0.6489283433133732, 2.1299306387225547, 1.3433385229540917, -1.3701403193612776, -1.635721756487026, 0.6554257485029941, 1.9922668662674652, -0.05604011976047904, -1.980084231536926, -0.7723790419161677, 1.6202904191616767, 1.2052686626746507, -1.0667927145708582, -1.1187719560878244, 0.6424309381237524, 0.9709559880239521, -0.36832165668662675, -0.7744094810379242, 0.08243582834331337, 0.544969860279441, -0.0032487025948103793, -0.41827045908183635, -0.16121686626746506, 0.13197854291417166, 0.15918642714570858, 0.05807055888223553, -0.024771357285429143, -0.05319750499001996, -0.049542714570858286, -0.05888273453093812, 0.10395848303393214, 0.2814188622754491, 0.030456586826347304, -0.4056817365269461, -0.17786646706586826, 0.5518733532934131, 0.5413150698602794, -0.23553093812375248, -0.4950210578842315, 0.08324800399201597, 0.34192594810379245, -0.15471946107784432, -0.4211130738522954, -0.037766167664670655, 0.3248702594810379, 0.2318761477045908, -0.15065858283433134, -0.4255800399201597, -0.11817155688622755, 0.5100463073852296, 0.4588792415169661, -0.27695189620758487, -0.542127245508982, 0.05197924151696607, 0.5758325349301396, 0.218475249500998, -0.376849500998004, -0.27370319361277445, 0.27573363273453094, 0.4548183632734531, -0.025583532934131736, -0.44507225548902196, -0.17583602794411177, 0.2793884231536926, 0.1372576846307385, -0.3329920159680639, -0.3195911177644711, 0.23593702594810378, 0.4434479041916167, -0.07025319361277445, -0.29603802395209583, 0.064161876247505, 0.13035419161676648, -0.2042621756487026, -0.1555316367265469, 0.22578483033932137, 0.13076027944111776, -0.2765458083832335, -0.09949151696606787, 0.5218228542914172, 0.3630425149700599, -0.46984361277445114, -0.7788764471057884, 0.11776546906187624, 1.1346093812375249, 0.4868993013972056, -0.8812105788423154, -0.7256789421157684, 1.1626294411177645, 1.4286169660678643, -0.8848653692614771, -2.1477985029940116, -0.013400898203592816, 2.333786726546906, 0.45156966067864274, -2.282619660678643, -0.9356263473053893, 2.317137125748503, 1.480190119760479, -1.8943997005988025, -2.2326708582834334, 0.4674070858283433, 2.3520606786427147, 0.5526855289421158, -1.8424204590818365, -1.3624246506986029, 1.750644610778443, 2.4036338323353292, -0.9092306387225548, -2.2854622754491016, 0.3082206586826347, 2.261097005988024, 0.2367492015968064, -1.8740953093812376, -0.8251704590818364, 1.4091247504990019, 1.1394824351297406, -0.949027245508982, -1.5017127744510979, -0.11817155688622755, 1.4700379241516965, 0.8154243512974052, -0.9863873253493014, -1.1902434131736528, 0.5457820359281437, 1.3575515968063872, -0.41786437125748505, -1.6141991017964072, 0.08852714570858283, 1.8387656686626745, 0.451975748502994, -1.3672977045908186, -0.6241569860279441, 1.081411876247505, 0.9047636726546906, -0.6891310379241516, -1.272679241516966, -0.3273067864271457, 1.0119708582834332, 0.8198913173652694, -0.6156291417165668, -0.9802960079840319, 0.42964091816367267, 1.172781636726547, -0.2200996007984032, -1.3108514970059881, -0.23593702594810378, 1.3222219560878246, 0.7504502994011977, -0.8921749500998004, -0.9973516966067864, 0.31431197604790423, 1.0858788423153694, 0.28751017964071857, -0.7374554890219561, -0.5612133732534931, 0.5717716566866267, 0.9677072854291416, -0.13035419161676648, -1.0075038922155688, -0.24121616766467066, 0.9100428143712576, 0.39674780439121754, -0.7362372255489023, -0.4852749500998004, 0.5051732534930139, 0.7203998003992016, 0.06619231536926148, -0.5551220558882236, -0.5368481037924152, 0.11329850299401198, 0.6481161676646706, 0.29197714570858285, -0.4686253493013972, -0.33380419161676644, 0.5754264471057884, 0.6066952095808383, -0.38212864271457087, -0.752480738522954, 0.05319750499001996, 0.719587624750499, 0.19898303393213573, -0.5587768463073852, -0.23228223552894212, 0.5916699600798403, 0.47106187624750495, -0.38375299401197605, -0.6749179640718562, -0.052385329341317365, 0.4548183632734531, 0.07553233532934131, -0.31553023952095804, 0.04507574850299401, 0.33583463073852293, -0.05116706586826347, -0.3244641716566866, -0.22537874251497006, -0.3106571856287425, -0.23147005988023953, 0.4255800399201597, 0.5088280439121756, -0.2988806387225549, -0.2680179640718563, 0.49217844311377246, 0.19573433133732535, -0.7029380239520958, -0.34314421157684627, 0.5742081836327345, 0.23147005988023953, -0.4982697604790419, 0.06741057884231537, 0.8710583832335329, 0.2205056886227545, -0.8937993013972055, -0.6517709580838323, 0.7094354291417166, 0.801617365269461, -0.6521770459081836, -1.279582734530938, 0.5551220558882236, 1.936226746506986, -0.1721812375249501, -2.3285075848303394, -0.41583393213572856, 2.6822100798403197, 1.0310569860279442, -2.404852095808383, -1.6064834331337325, 1.9723685628742513, 1.970338123752495, -1.3896325349301397, -2.1542959081836326, 0.4588792415169661, 2.311451896207585, 0.41299131736526945, -1.7827255489021956, -1.0233413173652695, 1.6434374251497006, 1.9439424151696605, -0.8747131736526946, -2.104347105788423, 0.03289311377245509, 2.135209780439122, 0.583142115768463, -1.537042415169661, -0.6517709580838323, 1.1439494011976048, 0.7143084830339321, -0.7273032934131736, -0.6672022954091816, 0.11451676646706586, 0.2781701596806387, -0.03329920159680639, -0.19573433133732535, -0.2765458083832335, -0.08730888223552895, 0.37969211576846307, 0.2765458083832335, -0.3484233532934132, -0.4142095808383234, 0.12832375249501, 0.3740068862275449, 0.19410998003992017, -0.008527844311377245, -0.18964301397205588, -0.16365339321357286, 0.28954061876247505, 0.41705219560878243, -0.20913522954091818, -0.5287263473053893, 0.2529927145708583, 0.7642572854291417, -0.10517674650698602, -0.7776581836327345, -0.07472015968063872, 0.6473039920159681, 0.16202904191616765, -0.583142115768463, -0.5185741516966068, 0.12345069860279441, 0.4868993013972056, 0.13766377245508982, -0.43816876247504993, -0.49217844311377246, 0.1746177644710579, 0.5315689620758484, -0.05888273453093812, -0.4816201596806387, 0.11695329341317366, 0.7033441117764471, 0.25745968063872254, -0.43979311377245506, -0.38375299401197605, 0.1417246506986028, 0.32162155688622757, 0.012182634730538922, -0.32527634730538924, -0.11898373253493014, 0.4389809381237525, 0.41624001996007987, -0.32974331337325347, -0.6968467065868263, -0.0004060878243512974, 0.565274251497006, 0.05928882235528942, -0.4686253493013972, 0.036953992015968065, 0.6318726546906188, 0.16405948103792414, -0.5108584830339321, -0.3289311377245509, 0.35004770459081835, 0.4077121756487026, -0.22375439121756488, -0.5551220558882236, 0.09380628742514971, 0.8921749500998004, 0.34517465069860276, -0.9177584830339321, -0.7451711576846307, 0.9510576846307386, 1.0550161676646705, -0.8694340319361278, -1.3847594810379242, 0.7057806387225549, 1.6970410179640718, -0.47390449101796406, -1.933384131736527, 0.06822275449101796, 2.1855646706586827, 0.5035489021956088, -1.8907449101796407, -1.1528833333333335, 1.3486176646706587, 1.6077016966067865, -0.6298422155688622, -1.7047566866267465, -0.09867934131736526, 2.045058283433134, 0.9884177644710579, -1.4972458083832334, -1.1508528942115768, 1.2239487025948104, 1.2072991017964072, -1.130548502994012, -1.4529822355289421, 0.4231435129740519, 1.2653696606786426, 0.06131926147704591, -0.7723790419161677, -0.3443624750499002, 0.22416047904191616, 0.37157035928143717, 0.030862674650698602, -0.4401992015968064, -0.44872704590818363, 0.28588582834331333, 0.662329241516966, -0.08446626746506986, -0.6131926147704592, 0.08690279441117765, 0.6858823353293413, 0.035735728542914166, -0.6091317365269461, -0.23350049900199601, 0.4036512974051896, 0.4617218562874251, -0.06010099800399202, -0.6217204590818364, -0.4507574850299401, 0.4471026946107784, 0.621314371257485, -0.28060668662674654, -0.6452735528942115, 0.28751017964071857, 0.8657792415169661, -0.11573502994011976, -0.9827325349301397, -0.1441611776447106, 0.9461846307385229, 0.357763373253493, -0.7991808383233533, -0.554309880239521, 0.7187754491017964, 0.9185706586826348, -0.2838553892215569, -0.9344080838323353, -0.2351248502994012, 0.6944101796407185, 0.49420888223552895, -0.47796536926147704, -0.5567464071856287, 0.4836505988023952, 0.8190791417165668, -0.17949081836327346, -0.8186730538922156, -0.24202834331337325, 0.531162874251497, 0.5189802395209581, -0.021928742514970057, -0.35654510978043913, -0.16040469061876247, 0.33867724550898204, 0.43979311377245506, -0.1868003992015968, -0.6615170658682635, -0.15959251497005986, 0.6440552894211576, 0.4588792415169661, -0.4588792415169661, -0.607507385229541, 0.24487095808383233, 0.6412126746506985, -0.08202974051896207, -0.6891310379241516, -0.16933862275449102, 0.7179632734530939, 0.5023306387225549, -0.4190826347305389, -0.5396907185628743, 0.14781596806387226, 0.3898443113772455, -0.15837425149700599, -0.3171545908183633, 0.23796746506986027, 0.3313676646706587, -0.08690279441117765, 0.03532964071856288, 0.2363431137724551, -0.2793884231536926, -0.8182669660678643, -0.1868003992015968, 0.858875748502994, 0.41989481037924153, -0.7151206586826347, -0.32162155688622757, 1.0026308383233533, 0.6647657684630738, -0.8568453093812375, -0.9145097804391218, 0.301723253493014, 0.5705533932135729, -0.29847455089820357, -0.4572548902195609, 0.38618952095808384, 0.6761362275449102, -0.2343126746506986, -0.9413115768463074, -0.12345069860279441, 0.6850701596806388, -0.05482185628742515, -0.7199937125748502, 0.29847455089820357, 1.0639500998003992, -0.28060668662674654, -1.3453689620758484, 0.14659770459081836, 1.6292243512974052, 0.22213003992015967, -1.568717265469062, -0.50395499001996, 1.492372754491018, 0.88892624750499, -1.0602953093812375, -1.205674750499002, 0.44872704590818363, 1.2946079840319362, 0.006903493013972056, -1.1614111776447107, -0.43329570858283434, 1.2280095808383233, 1.1057771457085828, -0.7601964071856288, -1.3360289421157683, 0.19817085828343314, 1.4083125748502994, 0.3281189620758483, -0.8324800399201597, -0.2964441117764471, 0.497051497005988, 0.3476111776447106, -0.14253682634730538, -0.22781526946107783, -0.24121616766467066, -0.17624211576846308, 0.23837355289421155, 0.4117730538922155, -0.14578552894211574, -0.4174582834331337, 0.15471946107784432, 0.4864932135728543, -0.024771357285429143, -0.6200961077844311, -0.4085243512974052, 0.4755288423153693, 0.8572513972055887, 0.031674850299401196, -0.9027332335329341, -0.5072036926147705, 0.8296374251497006, 0.892987125748503, -0.5413150698602794, -1.051361377245509, 0.3179667664670659, 1.3043540918163672, 0.08568453093812375, -1.2044564870259482, -0.44750878243512976, 1.0103465069860278, 0.7362372255489023, -0.7390798403193614, -1.0875031936127746, 0.2062926147704591, 1.1999895209580838, 0.2582718562874251, -1.1094319361277445, -0.8812105788423154, 0.614410878243513, 0.923037624750499, -0.3415198602794411, -0.8044599800399201, 0.17989690618762474, 0.9112610778443113, 0.248525748502994, -0.6371517964071857, -0.5246654690618763, 0.19492215568862276, 0.47187405189620757, 0.031674850299401196, -0.4588792415169661, -0.32162155688622757, 0.387813872255489, 0.5957308383233533, -0.17867864271457085, -0.7472015968063872, -0.2590840319361277, 0.395935628742515, 0.23796746506986027, -0.3378650698602794, -0.30537804391217566, 0.2720788423153693, 0.48974191616766466, 0.13603942115768464, -0.24162225548902197, -0.16040469061876247, 0.01908612774451098, 0.04182704590818363, 0.22659700598802396, 0.25339880239520957, -0.22497265469061875, -0.4422296407185629, 0.3561390219560878, 0.8856775449101796, -0.09989760479041916, -1.1691268463073852, -0.1912673652694611, 1.4793779441117765, 0.6838518962075849, -1.437956986027944, -1.1021223552894213, 1.428210878243513, 1.3985664670658684, -1.2430348303393215, -1.7607968063872257, 0.6160352295409182, 1.8574457085828344, 0.08933932135728542, -1.4217134730538923, -0.5746142714570858, 1.2361313373253493, 1.0777570858283432, -0.8560331337325349, -1.1622233532934132, 0.7553233532934132, 1.3839473053892215, -0.4852749500998004, -1.2637453093812376, 0.13400898203592815, 1.029432634730539, 0.06497405189620759, -0.6842579840319362, -0.015025249500998004, 0.3593877245508982, -0.13035419161676648, -0.24202834331337325, 0.18395778443113772, 0.14984640718562875, -0.15878033932135727, -0.03451746506986028, 0.18436387225548903, 0.012182634730538922, -0.14944031936127744, 0.0625375249500998, 0.19979520958083832, -0.057664471057884234, -0.3642607784431138, -0.3776616766467066, 0.08406017964071857, 0.5157315369261477, -0.036953992015968065, -0.8097391217564871, -0.31025109780439125, 0.6948162674650699, 0.41136696606786427, -0.6233448103792415, -0.6001978043912175, 0.47877754491017965, 0.8052721556886228, -0.15431337325349304, -0.6619231536926148, 0.015837425149700598, 0.7699425149700599, 0.39755998003992016, -0.6282178642714571, -0.7013136726546906, 0.3098450099800399, 0.8803984031936128, 0.1084254491017964, -0.7354250499001996, -0.3463929141716567, 0.5969491017964071, 0.5514672654690619, -0.3110632734530938, -0.5559342315369261, 0.02030439121756487, 0.5100463073852296, 0.15431337325349304, -0.5197924151696607, -0.5258837325349301, 0.25989620758483034, 0.7398920159680639, 0.0637557884231537, -0.6899432135728543, -0.3447685628742515, 0.549030738522954, 0.6188778443113773, -0.14862814371257485, -0.553091616766467, -0.0629436127744511, 0.5900456087824351, 0.38212864271457087, -0.3800982035928144, -0.44872704590818363, 0.37644341317365265, 0.7313641716566865, -0.03289311377245509, -0.6022282435129741, -0.06700449101796407, 0.5591829341317365, 0.1868003992015968, -0.4767471057884231, -0.3269006986027944, 0.21319610778443113, 0.18436387225548903, -0.09827325349301397, 0.007715668662674651, 0.07431407185628743, -0.1539072854291417, -0.13319680638722556, 0.06862884231536925, -0.22619091816367265, -0.5015184630738523, 0.12913592814371255, 0.7041562874251497, 0.03654790419161677, -0.6582683632734532, 0.07309580838323354, 0.8044599800399201, 0.11126806387225549, -0.7078110778443114, -0.2879162674650698, 0.5851725548902196, 0.4073060878243513, -0.39715389221556885, -0.4085243512974052, 0.29197714570858285, 0.4771531936127745, -0.2529927145708583, -0.7203998003992016, -0.09867934131736526, 0.5823299401197605, 0.07350189620758484, -0.6424309381237524, -0.04060878243512974, 0.6586744510978044, 0.014619161676646708, -0.5218228542914172, 0.2818249500998004, 0.7711607784431137, -0.1396942115768463, -0.7147145708582834, 0.05807055888223553};

		//double[] doubles = envelopeSpectrum(data);
		//DspUtil.main - result = [0.38545570342966257, 0.10118626790587514, 0.021936268226869744, 0.008495951628480788,
		//0.07419562602817306, 0.011609541744488815, 0.010650030699768812, 0.0045265078547011475]
		//System.out.println("DspUtil.main - result = " + Arrays.toString(doubles));

		//System.out.println("peak = " + getPeak(data));
		//System.out.println("peak2Peak = " + getPeakToPeak(data));
		//System.out.println("CF = " + getClearanceFactor(data));
		//System.out.println("SF = " + getSkewnessFactor(data));
		//System.out.println("Ku = " + getKurtosis(data));

		//System.out.println("DspUtil.main - " + Arrays.toString(Arrays.copyOfRange(data, 2, 6)));

		//Arrays.sort(data);
		//System.out.println("DspUtil.main - " + Arrays.toString(data));

		//轴承第三阶段机理模型值测试
		System.out.println("DspUtil.main - data.length = " + data.length);
		List<BigDecimal> ftfList = new ArrayList<>();
		ftfList.add(BigDecimal.valueOf(0.421));
		ftfList.add(BigDecimal.valueOf(4.0));
		ftfList.add(BigDecimal.valueOf(0.167));
		ftfList.add(BigDecimal.valueOf(10));
		int rank = getCharacteristicFreqRank(BigDecimal.valueOf(8), data,
			"{\"amplitudeUpper\":1.1,\"amplitudeLower\":0.1}", 33.33, ftfList);
		System.out.println("DspUtil.main - rank = " + rank);

		//测试傅里叶变换的振幅和相位特性
		double[] fftMagnitude = fftMagnitude(data);
		System.out.println("DspUtil.main - fftMagnitude.length = " + fftMagnitude.length);
		System.out.println("DspUtil.main - fftMagnitude = " + Arrays.toString(Arrays.copyOf(fftMagnitude, 1000)));

		double[] fftPhaseDeg = fftPhaseDeg(data);
		System.out.println("DspUtil.main - fftPhaseDeg.length = " + fftPhaseDeg.length);
		System.out.println("DspUtil.main - fftPhaseDeg = " + Arrays.toString(Arrays.copyOf(fftPhaseDeg, 1000)));

		System.out.println("DspUtil.main - timeDomainData = " + Arrays.toString(Arrays.copyOf(data, 1000)));

		// 测试倒频谱
		System.out.println("DspUtil.main - data.length = " + data.length);
		double[] cepstrum = cepstrum(data);
		System.out.println("DspUtil.main - cepstrum.length = " + cepstrum.length);
		System.out.println("DspUtil.main - cepstrum = " + Arrays.toString(cepstrum));
	}*/

}
