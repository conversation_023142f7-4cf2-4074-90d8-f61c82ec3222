package com.snszyk.sidas.basic.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckRecordDTO;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckRecord;
import com.snszyk.sidas.basic.vo.SpotCheckRecordVO;

import java.util.List;
import java.util.Map;

/**
 * 设备点检记录 service
 * <AUTHOR>
 */
public interface IEquipmentSpotCheckRecordService extends BaseService<EquipmentSpotCheckRecord> {

	/**
	 * 查询设备指定时间段的点检记录
	 * @param vo vo
	 * @return
	 */
	List<EquipmentSpotCheckRecordDTO> getSpotCheckRecord(SpotCheckRecordVO vo);

	/**
	 * 查询最近2个月的点检设备数
	 * @param tenantId 租户id
	 * @return
	 */
    List<Map<String, Object>> getLast2MonthsSpotCheckEquipmentCount(String tenantId);

	/**
	 * 获取点检数、点检设备数统计
	 * @param tenantId 租户id
	 * @param lastMonths 近 N 个月
	 * @return
	 */
    List<Map<String, Object>> getSpotCheckStatistics(String tenantId, Integer lastMonths);
}
