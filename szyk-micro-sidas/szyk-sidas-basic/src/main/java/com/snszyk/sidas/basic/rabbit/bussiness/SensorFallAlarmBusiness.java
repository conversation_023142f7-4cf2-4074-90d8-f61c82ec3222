package com.snszyk.sidas.basic.rabbit.bussiness;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.sidas.basic.dto.SensorDataDTO;
import com.snszyk.sidas.basic.dto.SensorInstanceDTO;
import com.snszyk.sidas.basic.dto.SensorOfflineDTO;
import com.snszyk.sidas.basic.entity.Monitor;
import com.snszyk.sidas.basic.entity.SensorInstance;
import com.snszyk.sidas.basic.entity.Wave;
import com.snszyk.sidas.basic.enums.MeasureDirectionEnum;
import com.snszyk.sidas.basic.enums.SampledDataTypeEnum;
import com.snszyk.sidas.basic.rabbit.handler.Command;
import com.snszyk.sidas.basic.rabbit.handler.CommandBusiness;
import com.snszyk.sidas.basic.rabbit.handler.MessageBean;
import com.snszyk.sidas.basic.service.IMonitorService;
import com.snszyk.sidas.basic.service.ISensorInstanceService;
import com.snszyk.sidas.basic.service.IWaveService;
import com.snszyk.sidas.basic.vo.SensorDataVO;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.feign.IUserSearchClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 传感器脱落报警
 *
 * <AUTHOR>
 * @date 2024/05/07 13:35
 **/
@Slf4j
@Component
@AllArgsConstructor
public class SensorFallAlarmBusiness implements CommandBusiness {

	private final ISensorInstanceService sensorInstanceService;
	private final IMonitorService monitorService;
	private final IWaveService waveService;
	private final ISysClient sysClient;
	private final IMessageClient messageClient;
	private final IUserSearchClient userSearchClient;
	private final RabbitTemplate rabbitTemplate;
	private final InfluxdbTools influxdbTools;
	private static final String[] SAMPLE_DATA_TYPE = {"ACCELERATION", "VELOCITY", "DISPLACEMENT"};

	@Override
	public String getCommand() {
		//return Command.VIBRATE_COMMAND;
		return null;
	}

	@Override
	public void business(MessageBean message) {
		// 仅加速度、速度和位移类型判断传感器脱落
		if (!ArrayUtils.contains(SAMPLE_DATA_TYPE, SampledDataTypeEnum.getByCode(message.getType()).getCode())) {
			return;
		}
		log.info("传感器脱落—输入数据：=============={}", message);
		Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
			.eq(Wave::getMonitorId, message.getMonitorId()).eq(Wave::getSensorCode, message.getId())
			.eq(Wave::getSampleDataType, message.getType()).eq(Wave::getSensorInstanceParamId, message.getSensorInstanceParamId())
			.eq(Wave::getUnbind, 0));
		if(wave == null){
			log.info("当前传感器没有波形，无法判断是否脱落：=============={}", message.getId() + StringPool.COLON + message.getType());
		}
		if (Func.isEmpty(wave.getHaltLine())) {
			return;
		}
		if (BigDecimal.ZERO.compareTo(wave.getHaltLine()) == 0) {
			return;
		}
		String sensorCode = wave.getSensorCode();
		BigDecimal value;
		if(Func.isNotEmpty(message.getValue())){
			value = new BigDecimal(message.getValue());
		} else {
			value = BigDecimal.valueOf(Func.toDouble(message.getEffective()));
		}
		log.info("开始判断当前传感器是否脱落==============波形：{}，传感器编码：{}，有效值：{}", wave.getId(), sensorCode, value);
		Monitor monitor = monitorService.getById(wave.getMonitorId());
		// 当前设备除当前部位之外的其他部位
		List<Monitor> monitorList = monitorService.list(Wrappers.<Monitor>query().lambda()
			.eq(Monitor::getEquipmentId, monitor.getEquipmentId()).ne(Monitor::getId, monitor.getId()));
		boolean flag = Boolean.FALSE;
		if (Func.isNotEmpty(monitorList)) {
			// 其他部位的波形按波形id正序，停机线不为0的第一个waveId，根据waveId查询最新的振动有效值，若有一个有效值大于等于自己波形的停机线，则可判断脱落
			// 三轴传感器Z轴是主轴，单轴的自己是主轴，查波形只查主轴的波形
			for (Monitor m : monitorList) {
				List<Wave> compareWaveList = waveService.list(Wrappers.<Wave>query().lambda()
					.eq(Wave::getMonitorId, m.getId()).eq(Wave::getUnbind, 0).eq(Wave::getSampleDataType, wave.getSampleDataType())
					.eq(Wave::getMeasureDirection, MeasureDirectionEnum.AXIAL.getCode()).gt(Wave::getHaltLine, BigDecimal.ZERO).orderByAsc(Wave::getId));
				if (Func.isEmpty(compareWaveList)) {
					compareWaveList = waveService.list(Wrappers.<Wave>query().lambda()
						.eq(Wave::getMonitorId, m.getId()).eq(Wave::getUnbind, 0).gt(Wave::getHaltLine, BigDecimal.ZERO)
						.eq(Wave::getSampleDataType, wave.getSampleDataType()).orderByAsc(Wave::getId));
				}
				if (Func.isNotEmpty(compareWaveList)) {
					Wave compareWave = compareWaveList.get(0);
					if(Func.isNotEmpty(compareWave.getHaltLine()) && compareWave.getHaltLine().compareTo(BigDecimal.ZERO) > 0){
						SensorDataVO sensorData = new SensorDataVO(null, compareWave.getId(), null);
						JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
						List<JSONObject> list = influxdbTools.queryData(null, null, jsonObject, (query) -> {
							query.addSort("_time", true);
							query.getQuerySQL().append("|> limit(n:1, offset:0)");
						});
						List<SensorDataDTO> sensorDataList = new ArrayList<>();
						list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
						if (Func.isNotEmpty(sensorDataList)) {
							BigDecimal rmsValue = sensorDataList.get(0).getValue();
							if (rmsValue.compareTo(compareWave.getHaltLine()) >= 0) {
								flag = true;
							}
						}
					}
				}
			}
			// 满足条件，发送报警
			if (value.compareTo(wave.getHaltLine()) < 0 && flag) {
				this.sendAlarmMsg(monitor.getTenantId(), sensorCode, MessageBizTypeEnum.SENSOR_FALL);
			}
		} else {
			// 只有一个部位，判断当前部位下面有几个振动传感器：若>1,则可判断是否脱落，否则无法判断
			List<SensorInstance> sensorInstanceList = sensorInstanceService.list(Wrappers.<SensorInstance>query().lambda()
				.eq(SensorInstance::getMonitorId, wave.getMonitorId()).ne(SensorInstance::getCode, sensorCode));
			if (Func.isNotEmpty(sensorInstanceList)) {
				for (SensorInstance s : sensorInstanceList) {
					List<Wave> compareWaveList = waveService.list(Wrappers.<Wave>query().lambda()
						.eq(Wave::getMonitorId, wave.getMonitorId()).eq(Wave::getSensorCode, s.getCode()).eq(Wave::getSampleDataType, wave.getSampleDataType())
						.eq(Wave::getMeasureDirection, MeasureDirectionEnum.AXIAL.getCode()).eq(Wave::getUnbind, 0)
						.gt(Wave::getHaltLine, BigDecimal.ZERO).orderByAsc(Wave::getId));
					if (Func.isEmpty(compareWaveList)) {
						compareWaveList = waveService.list(Wrappers.<Wave>query().lambda()
							.eq(Wave::getMonitorId, wave.getId()).eq(Wave::getSensorCode, s.getCode()).eq(Wave::getSampleDataType, wave.getSampleDataType())
							.eq(Wave::getUnbind, 0).gt(Wave::getHaltLine, BigDecimal.ZERO).orderByAsc(Wave::getId));
					}
					if (Func.isNotEmpty(compareWaveList)) {
						Wave compareWave = compareWaveList.get(0);
						if(Func.isNotEmpty(compareWave.getHaltLine()) && compareWave.getHaltLine().compareTo(BigDecimal.ZERO) > 0){
							SensorDataVO sensorData = new SensorDataVO(null, compareWave.getId(), null);
							JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
							List<JSONObject> list = influxdbTools.queryData(null, null, jsonObject, (query) -> {
								query.addSort("_time", true);
								query.getQuerySQL().append("|> limit(n:1, offset:0)");
							});
							List<SensorDataDTO> sensorDataList = new ArrayList<>();
							list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
							if (Func.isNotEmpty(sensorDataList)) {
								BigDecimal rmsValue = sensorDataList.get(0).getValue();
								if (rmsValue.compareTo(compareWave.getHaltLine()) >= 0) {
									flag = true;
								}
							}
						}
					}
				}
				// 满足条件，发送报警
				if(Func.isNotEmpty(wave.getHaltLine()) && wave.getHaltLine().compareTo(BigDecimal.ZERO) > 0){
					if (value.compareTo(wave.getHaltLine()) < 0 && flag) {
						this.sendAlarmMsg(monitor.getTenantId(), sensorCode, MessageBizTypeEnum.SENSOR_FALL);
					}
				}
			}
		}
	}

	/**
	 * 发送sidas门户端待办消息
	 *
	 * @param tenantId
	 * @param sensorCode
	 * @param messageBizType
	 * @return void
	 * <AUTHOR>
	 * @date 2024/4/30 15:08
	 */
	private void sendAlarmMsg(String tenantId, String sensorCode, MessageBizTypeEnum messageBizType) {
		log.info("租户id：=============={}", tenantId);
		R<List<User>> allUserResult = userSearchClient.listAllUser(tenantId);
		if (allUserResult.isSuccess() && Func.isNotEmpty(allUserResult.getData())) {
			//传感器实例（测点路径）
			SensorInstanceDTO sensorInstance = sensorInstanceService.detailByCode(sensorCode);
			//发送待办消息
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SiDAs");
			messageVo.setTitle(messageBizType.getMessage() + "通知");
			SensorOfflineDTO messageContent = new SensorOfflineDTO()
				.setPathName(sensorInstance.getPathName().replace(StringPool.COMMA, StringPool.SLASH))
				.setSensorCode(sensorInstance.getCode());
			messageVo.setContent(JSONObject.toJSONString(messageContent));
			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			messageVo.setBizType(messageBizType.getCode());
			messageVo.setBizId(sensorInstance.getId().toString());
			messageVo.setSender("SiDAs");
			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			List<ReceiverInfoVo.UserVo> userVoList = allUserResult.getData().stream().map(user -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(user.getId());
				userVo.setRealName(user.getRealName());
				return userVo;
			}).collect(Collectors.toList());
			receiverInfoVo.setUserList(userVoList);
			messageVo.setReceiverInfoVo(receiverInfoVo);
			log.info("发送" + messageBizType.getMessage() + "通知 - 接收人：{}", JSONObject.toJSONString(userVoList));
			R messageResult = messageClient.pushMessage(messageVo);
			log.info("发送" + messageBizType.getMessage() + "通知 - 结果：{}", JSONObject.toJSONString(messageResult));
			// 发送钉钉消息
			log.info(messageBizType.getMessage() + "-发送钉钉消息：==================={}", sensorCode);
			R<Tenant> tenantInfo = sysClient.getTenant(tenantId);
			DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenantInfo.getData().getTenantId(),
				tenantInfo.getData().getTenantName(), sensorCode, messageBizType.getCode(), JSONUtil.toJsonStr(sensorInstance));
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
				EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
		} else {
			log.warn("获取用户列表失败！code = {}, msg = {}.", allUserResult.getCode(), allUserResult.getMsg());
		}
	}

}
