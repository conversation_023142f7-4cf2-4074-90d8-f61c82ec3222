/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.sidas.basic.entity.DeviceModelConfig;
import org.apache.ibatis.annotations.Param;

/**
 * 3D模型配置表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface DeviceModelConfigMapper extends BaseMapper<DeviceModelConfig> {

	/**
	 * 根据传感器编码删除
	 *
	 * @param sensorCode
	 * @param category
	 * @return
	 */
	Integer removeBySensorCode(@Param("sensorCode") String sensorCode, @Param("category") Integer category);

	/**
	 * 根据设备id物理删除
	 *
	 * @param equipmentId
	 * @param category
	 * @return
	 */
	Integer removeByEquipment(@Param("equipmentId") Long equipmentId, @Param("category") Integer category);

	/**
	 * 根据测点id物理删除
	 *
	 * @param monitorId
	 * @param category
	 * @return
	 */
	Integer removeByMonitor(@Param("monitorId") Long monitorId, @Param("category") Integer category);

}
