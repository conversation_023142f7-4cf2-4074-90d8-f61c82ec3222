package com.snszyk.sidas.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckRecordDTO;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckRecord;
import com.snszyk.sidas.basic.vo.SpotCheckRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备点检记录 mapper
 * <AUTHOR>
 */
@Mapper
public interface EquipmentSpotCheckRecordMapper extends BaseMapper<EquipmentSpotCheckRecord> {

	List<EquipmentSpotCheckRecordDTO> getSpotCheckRecord(@Param("vo") SpotCheckRecordVO vo);

    List<Map<String, Object>> getLast2MonthsSpotCheckEquipmentCount(@Param("tenantId") String tenantId);

	List<Map<String, Object>> getSpotCheckStatistics(@Param("tenantId") String tenantId,
													 @Param("lastMonths") Integer lastMonths);
}
