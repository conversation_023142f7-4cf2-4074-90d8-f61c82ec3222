package com.snszyk.sidas.basic.rabbit.bussiness;

import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.sidas.basic.enums.NonVibrationDataEnum;
import com.snszyk.sidas.basic.enums.SampledDataTypeEnum;
import com.snszyk.sidas.basic.rabbit.handler.Command;
import com.snszyk.sidas.basic.rabbit.handler.MessageBean;
import com.snszyk.sidas.basic.service.ICollectionStationChannelService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 1、波形类型（加速度、速度、位移、温度）保存数据后，将最新的数据存到Redis
 *		key为 "sensorCode:sensorInstanceParamId:sampleDataType"
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class VibrateSaveRedisBusiness extends AbstractBusiness {

	private final ICollectionStationChannelService collectionStationChannelService;
	private final SzykRedis szykRedis;

	@Override
	public String getCommand() {
		return Command.VIBRATE_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);

		// 传感器数据最新数据
		if (SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode().equals(message.getType())) {
			// 温度
			String tempKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
				+ StringPool.COLON + message.getType();
			szykRedis.set(tempKey, message.getValue());
		} else {
			// 加速度、速度、位移（6种通用指标有哪个，就存哪个）
			// 有效值
			String effectiveKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
				+ StringPool.COLON + NonVibrationDataEnum.EFFECTIVE_VALUE.getCode();
			if (message.getValue() != null) {
				szykRedis.set(effectiveKey, message.getValue());
			} else if (message.getEffective() != null) {
				szykRedis.set(effectiveKey, message.getEffective());
			}

			// 峰值
			if (message.getPeak() != null) {
				String peakKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.PEAK_VALUE.getCode();
				szykRedis.set(peakKey, message.getPeak());
			}

			// 峰峰值
			if (message.getPeakPeak() != null) {
				String peakPeakKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.PEAK_PEAK_VALUE.getCode();
				szykRedis.set(peakPeakKey, message.getPeakPeak());
			}

			// 裕度
			if (message.getClearance() != null) {
				String clearanceKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.CLEARANCE_FACTOR.getCode();
				szykRedis.set(clearanceKey, message.getClearance());
			}

			// 歪度
			if (message.getSkewness() != null) {
				String skewnessKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.SKEWNESS_VALUE.getCode();
				szykRedis.set(skewnessKey, message.getSkewness());
			}

			// 峭度
			if (message.getKurtosis() != null) {
				String kurtosisKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.KURTOSIS_VALUE.getCode();
				szykRedis.set(kurtosisKey, message.getKurtosis());
			}
		}

	}


}
