<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.basic.mapper.DeviceModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceModelResultMap" type="com.snszyk.sidas.basic.entity.DeviceModel">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="attach_id" property="attachId"/>
        <result column="attach_link" property="attachLink"/>
        <result column="original_name" property="originalName"/>
        <result column="extension" property="extension"/>
        <result column="attach_size" property="attachSize"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="deviceModelDTOResultMap" type="com.snszyk.sidas.basic.dto.DeviceModelDTO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="attach_id" property="attachId"/>
        <result column="attach_link" property="attachLink"/>
        <result column="original_name" property="originalName"/>
        <result column="extension" property="extension"/>
        <result column="attach_size" property="attachSize"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="page" resultMap="deviceModelDTOResultMap">
        select * from eolm_device_model where is_deleted = 0
        <if test="deviceModel.name!=null and deviceModel.name!=''">
            and name like concat(concat('%', #{deviceModel.name}),'%')
        </if>
        order by create_time desc
    </select>

</mapper>
