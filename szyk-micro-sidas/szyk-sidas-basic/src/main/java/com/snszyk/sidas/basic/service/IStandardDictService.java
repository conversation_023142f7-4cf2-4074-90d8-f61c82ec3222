/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.sidas.basic.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.entity.StandardDict;
import com.snszyk.sidas.basic.vo.StandardDictVO;

import java.util.List;

/**
 * ISO10816标准字典 服务类
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
public interface IStandardDictService extends BaseService<StandardDict> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param standardDict
	 * @return
	 */
	IPage<StandardDict> page(IPage<StandardDict> page, StandardDictVO standardDict);

	/**
	 * 新增或修改
	 *
	 * @param standardDict
	 * @return
	 */
	boolean submit(StandardDictVO standardDict);

	/**
	 * 删除字典
	 *
	 * @param ids
	 * @return
	 */
	boolean removeDict(String ids);

	/**
	 * 保存通用指标标准门限
	 *
	 * @param standardDict
	 * @return
	 */
	boolean submitGeneral(StandardDictVO standardDict);

	/**
	 * 应用通用指标标准门限
	 *
	 * @param applyId
	 * @param waveIds
	 * @return
	 */
	boolean applyGeneral(Long applyId, List<Long> waveIds);

}
