/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service.logic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckConfigDTO;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckRecordDTO;
import com.snszyk.sidas.basic.dto.IntelligentDiagnosisResult;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckConfig;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckRecord;
import com.snszyk.sidas.basic.entity.Monitor;
import com.snszyk.sidas.basic.entity.SensorData;
import com.snszyk.sidas.basic.enums.FaultSuggestionEnum;
import com.snszyk.sidas.basic.enums.VibrationTypeEnum;
import com.snszyk.sidas.basic.feign.PythonServerFeign;
import com.snszyk.sidas.basic.service.*;
import com.snszyk.sidas.basic.vo.EquipmentSpotCheckConfigVO;
import com.snszyk.sidas.basic.vo.SpotCheckRecordVO;
import com.snszyk.sidas.basic.vo.SpotCheckSensorDataVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备点检计划配置表 逻辑服务实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@AllArgsConstructor
@Service
public class EquipmentSpotCheckConfigLogicService {

	private final IEquipmentSpotCheckConfigService equipmentSpotCheckConfigService;
	private final IEquipmentSpotCheckRecordService equipmentSpotCheckRecordService;
	private final IEquipmentService equipmentService;
	private final ISensorInstanceParamService sensorInstanceParamService;
	private final IMonitorService monitorService;
	private final PythonServerFeign pythonServerFeign;
	private final RabbitTemplate rabbitTemplate;

	/**
	 * 设备点检计划分页列表
	 * @param page page
	 * @param configVO  vo
	 * @return
	 */
	public IPage<EquipmentSpotCheckConfigDTO> page(IPage<EquipmentSpotCheckConfigDTO> page, EquipmentSpotCheckConfigVO configVO) {
		List<EquipmentSpotCheckConfigDTO> records = equipmentSpotCheckConfigService.selectConfigPage(page, configVO);
		return page.setRecords(records);
	}

	/**
	 * 更新 设备点检计划配置
	 * @param config config
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean update(EquipmentSpotCheckConfig config) {
		return equipmentSpotCheckConfigService.updateById(config);
	}

	/**
	 * 删除 设备点检计划配置 - 同步更新设备的needSpotCheck字段
	 * @param configIdList 配置id列表
	 * @return result
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeByIds(List<Long> configIdList) {
		// 查询设备点检计划配置
		List<EquipmentSpotCheckConfig> configList = equipmentSpotCheckConfigService.listByIds(configIdList);
		if (Func.isEmpty(configList)) {
			throw new ServiceException("当前设备点检计划配置已删除，请刷新后重试!");
		}

		// 删除设备点检计划配置
		boolean removeConfig = equipmentSpotCheckConfigService.removeByIds(configIdList);
		log.info("删除{}条设备点检计划配置：{}", configIdList.size(), removeConfig);

		// 更新设备needSpotCheck
		int count = equipmentService.resetEquipmentNeedSpotCheck(configList.stream()
			.map(EquipmentSpotCheckConfig::getEquipmentId)
			.collect(Collectors.toList()));
		log.info("重置了{}条设备的needSpotCheck。", count);

		return removeConfig && count > 0;
	}

	/**
	 * 上传点检仪的传感器数据
	 * @param sensorDataVO 传感器数据
	 * @return 上传结果
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean uploadSpotCheckSensorData(SpotCheckSensorDataVO sensorDataVO) {
		//参数校验
		if (sensorDataVO.getEquipmentId() == null || sensorDataVO.getMonitorId() == null) {
			log.warn("uploadSpotCheckSensorData() - 参数异常！equipmentId = {}, monitorId = {}",
				sensorDataVO.getEquipmentId(), sensorDataVO.getMonitorId());
			throw new ServiceException("参数异常！equipmentId = " + sensorDataVO.getEquipmentId()
				+ ", monitorId = " + sensorDataVO.getMonitorId());
		}

		// 获取测点的传感器列表
		List<SpotCheckSensorDataVO.SensorVO> sensorList = sensorDataVO.getSensorList();
		if (CollectionUtil.isNotEmpty(sensorList)) {
			sensorList.forEach(sensorVO -> {
				// 获取某个实例化的传感器的数据列表
				List<SpotCheckSensorDataVO.DataVO> dataList = sensorVO.getDataList();
				if (CollectionUtil.isNotEmpty(dataList)) {
					// 解析某个位号的传感器数据 & 保存点检记录 & 发送MQ
					List<EquipmentSpotCheckRecord> spotCheckRecordList = new ArrayList<>();
					dataList.forEach(dataVO -> {
						//1、保存点检记录
						EquipmentSpotCheckRecord spotCheckRecord = new EquipmentSpotCheckRecord()
							.setEquipmentId(sensorDataVO.getEquipmentId())
							.setMonitorId(sensorDataVO.getMonitorId())
							.setSensorCode(sensorVO.getSensorCode())
							.setVibrationType(dataVO.getVibrationType())
							.setOutputType(dataVO.getOutputType())
							.setCharacteristicValue(VibrationTypeEnum.NON_VIBRATION.getCode().equals(dataVO.getVibrationType()) ?
								dataVO.getCharacteristicValue() :
								BigDecimal.valueOf(getRMS(jsonArray2DoubleArray(JSON.parseArray(dataVO.getTimeDomainWaveform())))))
							.setTimeDomainWaveform(dataVO.getTimeDomainWaveform())
							.setOriginTime(dataVO.getOriginTime());
						spotCheckRecordList.add(spotCheckRecord);

						//TODO 暂时不可用 - 后续修改！
						throw new ServiceException("操作失败！");

						// 2、查找iotCode
						//DaqConfig daqConfig = daqConfigService.getOne(new QueryWrapper<DaqConfig>().lambda()
						//	.eq(DaqConfig::getEquipmentId, sensorDataVO.getEquipmentId())
						//	.eq(DaqConfig::getMonitorId, sensorDataVO.getMonitorId())
						//	.eq(DaqConfig::getSensorCode, sensorVO.getSensorCode())
						//	.eq(DaqConfig::getVibrationType, dataVO.getVibrationType())
						//	.eq(DaqConfig::getOutputType, dataVO.getOutputType())
						//	.eq(DaqConfig::getIsInvalid, 0));
						//if (daqConfig != null) {
						//	//3、查询波形配置
						//	//WaveConfig waveConfig = waveConfigService.getOne(new QueryWrapper<WaveConfig>().lambda()
						//	//	.eq(WaveConfig::getIotCode, daqConfig.getIotCode()));
						//	//if (waveConfig == null) {
						//	//	throw new ServiceException("未找到波形配置。monitorId = " + sensorDataVO.getMonitorId()
						//	//		+ ", sensorCode = " + sensorVO.getSensorCode() + ", iotCode = " + daqConfig.getIotCode());
						//	//}
						//
						//	//4、保存原始数据
						//	SensorRawData sensorRawData = saveSensorRawData(dataVO, daqConfig, "波形");
						//
						//	//5、发送传感器数据到MQ - 带RawDataId
						//	if (InvalidEnum.VALID.getCode().equals(sensorRawData.getInvalid())) {
						//		sendPointValueMQ(dataVO, daqConfig, sensorRawData);
						//	} else {
						//		throw new ServiceException("传感器数据异常！异常原因：" + sensorRawData.getInvalidReason());
						//	}
						//} else {
						//	log.warn("uploadSpotCheckSensorData() - 未获取到位号信息！monitorId = {}, sensorCode = {}, " +
						//			"vibrationType = {}, outputType = {}.", sensorDataVO.getMonitorId(), sensorVO.getSensorCode(),
						//		dataVO.getVibrationType(), dataVO.getOutputType());
						//	throw new ServiceException("未获取到位号信息！monitorId = " + sensorDataVO.getMonitorId()
						//		+ ", sensorCode = " + sensorVO.getSensorCode());
						//}
					});

					// 保存点检记录
					boolean saveBatch = equipmentSpotCheckRecordService.saveBatch(spotCheckRecordList);
					log.info("uploadSpotCheckSensorData() - 保存点检记录：{}", saveBatch);
				} else {
					log.warn("uploadSpotCheckSensorData() - 传感器没有采集数据！sensorCode = {}", sensorVO.getSensorCode());
				}
			});
		} else {
			log.warn("uploadSpotCheckSensorData() - 测点的传感器列表为空！monitorId = {}", sensorDataVO.getMonitorId());
			return false;
		}

		return true;
	}

	/**
	 * 发送传感器数据到MQ
	 * @param dataVO 传感器数据
	 * @param daqConfig 传感器配置
	 * @param sensorRawData 传感器原始数据
	 */
	//private void sendPointValueMQ(SpotCheckSensorDataVO.DataVO dataVO, DaqConfig daqConfig, SensorRawData sensorRawData) {
	//	PointValue pointValue = new PointValue();
	//	pointValue.setId("12345678");
	//	pointValue.setDeviceId("12345678");
	//	pointValue.setPointId("12345678");
	//	pointValue.setRawDataId(sensorRawData.getId());
	//	pointValue.setDevicePointBindCode(daqConfig.getIotCode());
	//	if (VibrationTypeEnum.NON_VIBRATION.getCode().equals(daqConfig.getVibrationType())) {
	//		pointValue.setValue(dataVO.getCharacteristicValue().toString());
	//	} else {
	//		pointValue.setValue(dataVO.getTimeDomainWaveform());
	//	}
	//	pointValue.setOriginTime(dataVO.getOriginTime());
	//	pointValue.setCreateTime(DateUtil.now());
	//	rabbitTemplate.convertAndSend(EolmConstant.Rabbit.FANOUT_EXCHANGE_VALUE, "", pointValue);
	//	log.info("uploadSpotCheckSensorData() - 发送传感器数据到MQ，iotCode = {}。", daqConfig.getIotCode());
	//}

	/**
	 * 保存点检数据到SensorRawData表
	 * @param dataVO 传感器数据
	 * @param daqConfig 采集配置
	 * @param wave 波形
	 * @return 存储的原始数据
	 */
	//private SensorRawData saveSensorRawData(SpotCheckSensorDataVO.DataVO dataVO, DaqConfig daqConfig, String wave) {
	//	SensorRawData sensorRawData = new SensorRawData();
	//	BeanUtils.copyProperties(daqConfig, sensorRawData);
	//	sensorRawData.setId(null)
	//		.setDaqConfigId(daqConfig.getId())
	//		.setSensorInstanceId(sensorInstanceParamService.getById(daqConfig.getSensorParamId()).getInstanceId())
	//		.setSensorInstanceParamId(daqConfig.getSensorParamId())
	//		//.setWaveConfigId(waveConfig.getId())
	//		.setOriginTime(dataVO.getOriginTime())
	//		.setInvalid(InvalidEnum.VALID.getCode())
	//		.setCreateTime(DateUtil.now());
	//
	//	if (VibrationTypeEnum.NON_VIBRATION.getCode().equals(daqConfig.getVibrationType())) {
	//		sensorRawData.setCharacteristicValue(dataVO.getCharacteristicValue());
	//	} else {
	//		//设置波形数据
	//		sensorRawData.setTimeDomainWaveform(dataVO.getTimeDomainWaveform());
	//		JSONArray valueArray = JSON.parseArray(sensorRawData.getTimeDomainWaveform());
	//		double[] valueData = jsonArray2DoubleArray(valueArray);
	//		// 校验采样点数（波形配置的）
	//		//if (BigDecimal.valueOf(waveConfig.getSamplingPoints()).compareTo(BigDecimal.valueOf(valueData.length)) != 0) {
	//		//	log.warn("传感器采集的数据个数{}与波形配置的采样点数{}不一致！", valueData.length, waveConfig.getSamplingPoints());
	//		//	sensorRawData.setInvalid(InvalidEnum.INVALID.getCode());
	//		//	sensorRawData.setInvalidReason(EolmConstant.InvalidReason.INCONSISTENT_SAMPLING_FREQ);
	//		//} else {
	//		//	//计算特征值（6个通用指标）
	//		//	sensorRawData.setCharacteristicValue(BigDecimal.valueOf(DspUtil.getRMS(valueData)));
	//		//	sensorRawData.setPeakValue(BigDecimal.valueOf(DspUtil.getPeak(valueData)));
	//		//	sensorRawData.setPeakPeakValue(BigDecimal.valueOf(DspUtil.getPeakToPeak(valueData)));
	//		//	sensorRawData.setClearanceValue(BigDecimal.valueOf(DspUtil.getClearanceFactor(valueData)));
	//		//	sensorRawData.setSkewnessValue(BigDecimal.valueOf(DspUtil.getSkewnessFactor(valueData)));
	//		//	sensorRawData.setKurtosisValue(BigDecimal.valueOf(DspUtil.getKurtosis(valueData)));
	//		//}
	//	}
	//
	//	if (CollectionUtil.isNotEmpty(getExistedDataList(daqConfig.getIotCode(), dataVO.getOriginTime()))) {
	//		log.warn("saveSensorRawData() - 该原始数据已存在！iotCode = {}, originTime = {}.",
	//			daqConfig.getIotCode(), dataVO.getOriginTime());
	//		throw new ServiceException("该传感器数据已存在，请勿重复上传！iotCode = " + daqConfig.getIotCode() + ", originTime = " + dataVO.getOriginTime());
	//	} else {
	//		boolean save = sensorRawDataService.save(sensorRawData);
	//		log.info("saveSensorRawData() - 保存原始数据：{}", save);
	//		return sensorRawData;
	//	}
	//}

	/**
	 * TODO 查询是否已存在相同的原始数据（sensor_instance_param_id & origin_time 唯一）
	 * @param sensorInstanceParamId 传感器实例参数id - 对应原来的位号
	 * @param originTime 数据采集时间
	 */
	private List<SensorData> getExistedDataList(String sensorInstanceParamId, Date originTime) {
		return null;
	}

	/**
	 * 计算RMS均方根值（有效值）
	 * @param data 输入数据
	 * @return rms
	 */
	private double getRMS(double[] data) {
		if (data == null || data.length == 0) {
			return 0d;
		}

		BigDecimal sum = BigDecimal.ZERO;
		for (Double aDouble : data) {
			sum = sum.add(BigDecimal.valueOf(Math.pow(aDouble, 2)));
		}

		return Math.sqrt(sum.doubleValue() / data.length);
	}

	/**
	 * jsonArray数组转double数组
	 * @param jsonArray 原数据
	 * @return double数组
	 */
	private double[] jsonArray2DoubleArray(JSONArray jsonArray) {
		if (jsonArray == null || jsonArray.size() == 0) {
			return null;
		}

		double[] data = new double[jsonArray.size()];
		for (int i = 0; i < jsonArray.size(); i++) {
			data[i] = jsonArray.getDoubleValue(i);
		}

		return data;
	}

	/**
	 * 查询设备指定时间段的点检记录
	 * @param vo vo
	 * @return
	 */
	public List<EquipmentSpotCheckRecordDTO> getSpotCheckRecord(SpotCheckRecordVO vo) {
		return equipmentSpotCheckRecordService.getSpotCheckRecord(vo);
	}

	/**
	 * 智能诊断
	 * @param originTime 采集时间
	 * @param daqConfigId 采集配置id
	 * @param waveConfigId 波形配置id
	 * @return
	 */
	public IntelligentDiagnosisResult getIntelligentDiagnosis(String originTime, String daqConfigId, String waveConfigId) {

		//TODO 暂时不可用 - 后续修改！
		throw new ServiceException("操作失败！");

		// 校验daqConfigId、waveConfigId匹配性，不匹配则提示同步数据
		//List<WaveConfig> waveConfigList = waveConfigService.list(Wrappers.<WaveConfig>lambdaQuery()
		//	.eq(WaveConfig::getDaqConfigId, daqConfigId)
		//	.eq(WaveConfig::getUnbind, 0));
		//if (Func.isEmpty(waveConfigId)
		//	|| waveConfigList.size() > 1
		//	|| !waveConfigList.get(0).getId().equals(Long.parseLong(waveConfigId))) {
		//	throw new ServiceException("采集配置、波形配置不匹配，请重新同步数据！");
		//}

		//调用机理模型获取故障类型
//		JSONObject mechanismModel = pythonServerFeign.mechanismModel(originTime, waveConfigId, PythonServerFeign.SEND_ALARM_NO);
//		log.info("机理模型结果：{}", mechanismModel);
//
//		return getIntelligentDiagnosis(mechanismModel);
	}

	/**
	 * 获取机理模型诊断结论、建议
	 * @param mechanismResult 机理模型诊断结果
	 *              {
	 * 	        	"alarmLevel": fault_grade,
	 * 	        	"monitorId": monitor_id,
	 * 	        	"result": data, # data="0"表示正常；data="1,2,3" 表示存在故障，多个故障用','分隔
	 * 	        	"waveConfigId": wave_config_id,
	 * 	        	"originTime": origin_time
	 * 	    	   }
	 * @return
	 */
	public IntelligentDiagnosisResult getIntelligentDiagnosis(JSONObject mechanismResult) {
		IntelligentDiagnosisResult result = new IntelligentDiagnosisResult();
		if (mechanismResult == null) {
			result.setSuccess(false);
			result.setFailReason("机理模型诊断返回空！诊断失败！");
		} else if (mechanismResult.getIntValue("code") == 1) {
			//未配置：{"msg":"未查询到机理模型配置信息","code":1}
			result.setSuccess(false);
			result.setFailReason("未查询到机理模型配置信息，无法进行诊断！");
		} else if (Func.isEmpty(mechanismResult.getString("result")) || "0".equals(mechanismResult.getString("result"))) {
			result.setSuccess(true);
			long monitorId = mechanismResult.getLongValue("monitorId");
			Monitor monitor = monitorService.getById(monitorId);
			result.setConclusion(String.format("通过模型分析，该%s未发现明显异常。",
				monitor.getEquipmentType() == 0 ? "设备" : DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType())));
		} else {
			result.setSuccess(true);
			String faultTypeResult = mechanismResult.getString("result");
			if (Func.isNotEmpty(faultTypeResult)) {
				// 测点
				long monitorId = mechanismResult.getLongValue("monitorId");
				Monitor monitor = monitorService.getById(monitorId);

				// 故障列表
				List<String> faultTypeList = Arrays.asList(faultTypeResult.split(StringPool.COMMA));
				List<String> faultTypeStringList = new ArrayList<>();
				faultTypeList.forEach(faultType -> faultTypeStringList.add(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, faultType)));

				// 故障等级
				Integer alarmLevel = mechanismResult.getInteger("alarmLevel");
				result.setConclusion(String.format("通过模型分析，该%s存在%s，故障为%s级",
					monitor.getEquipmentType() == 0 ? "设备" : DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType()),
					Func.join(faultTypeStringList, "、"), alarmLevel.toString()));

				// 诊断建议
				List<String> faultSuggestionList = new ArrayList<>();
				faultTypeList.forEach(faultType -> faultSuggestionList.add(FaultSuggestionEnum.getByCode(Integer.parseInt(faultType)).getSuggestion()));
				List<String> suggestionList = faultSuggestionList.stream().distinct().collect(Collectors.toList());
				result.setSuggestion(Func.join(suggestionList, "；"));
			} else {
				log.warn("机理模型返回的result为空！！！");
			}
		}

		return result;
	}

}
