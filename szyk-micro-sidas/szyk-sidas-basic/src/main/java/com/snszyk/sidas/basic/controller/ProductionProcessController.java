/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.resource.vo.AttachVO;
import com.snszyk.sidas.basic.dto.ProductionProcessDTO;
import com.snszyk.sidas.basic.entity.ProductionProcess;
import com.snszyk.sidas.basic.service.IProductionProcessService;
import com.snszyk.sidas.basic.vo.ProductionProcessVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 生产工艺流程配置表 控制器
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("/productionProcess")
@Api(value = "生产工艺流程配置表", tags = "生产工艺流程配置表接口")
public class ProductionProcessController extends SzykController {

	private final IProductionProcessService productionProcessService;

	/**
	 * 列表 生产工艺流程配置表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "列表", notes = "传入szykUser")
	public R<List<ProductionProcessVO>> list(SzykUser szykUser) {
		return R.data(productionProcessService.getList((szykUser.getTenantId())));
	}

	/**
	 * 保存图片 生产工艺流程配置表
	 */
	@PostMapping("/saveImage")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "保存图片", notes = "传入originalImage")
	public R<AttachVO> saveImage(@ApiParam(value = "图片id", required = true) @RequestParam Long originalImage, SzykUser szykUser) {
		return R.data(productionProcessService.saveImage(szykUser.getTenantId(), originalImage));
	}

	/**
	 * 修改 生产工艺流程配置表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "修改", notes = "传入productionProcess")
	public R update(@Valid @RequestBody ProductionProcess productionProcess) {
		return R.status(productionProcessService.updateById(productionProcess));
	}

	/**
	 * 新增或修改 生产工艺流程配置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增或修改", notes = "传入productionProcess")
	public R<ProductionProcess> submit(@Valid @RequestBody ProductionProcess productionProcess) {
		productionProcess.setCreateTime(DateUtil.now());
		productionProcessService.saveOrUpdate(productionProcess);
		return R.data(productionProcess);
	}

	/**
	 * 删除 生产工艺流程配置表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入szykUser")
	public R remove(SzykUser szykUser) {
		return R.data(productionProcessService.removeByTenant(szykUser.getTenantId()));
	}

	/**
	 * 删除单个区域 生产工艺流程配置表
	 */
	@PostMapping("/removeSingle")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除单个区域", notes = "传入id")
	public R removeSingle(Long id) {
		return R.data(productionProcessService.removeById(id));
	}

	/**
	 * 门户端展示 生产工艺流程配置表
	 */
	@GetMapping("/portalDisplay")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "门户端展示", notes = "传入szykUser")
	public R<ProductionProcessDTO> portalDisplay(SzykUser szykUser) {
		return R.data(productionProcessService.portalDisplay((szykUser.getTenantId())));
	}

	/**
	 * 删除图片 生产工艺流程配置表
	 */
	@PostMapping("/removeImage")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除图片", notes = "传入szykUser")
	public R removeImage(SzykUser szykUser) {
		return R.data(productionProcessService.removeImage(szykUser.getTenantId()));
	}

}
