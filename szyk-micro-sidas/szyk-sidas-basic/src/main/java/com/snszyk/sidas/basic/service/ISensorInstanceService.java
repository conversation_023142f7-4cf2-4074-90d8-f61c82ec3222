/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.dto.EquipmentSensorDTO;
import com.snszyk.sidas.basic.dto.EqumentSensorListDTO;
import com.snszyk.sidas.basic.dto.SensorInstanceBindExcelDTO;
import com.snszyk.sidas.basic.dto.SensorInstanceDTO;
import com.snszyk.sidas.basic.entity.SensorInstance;
import com.snszyk.sidas.basic.vo.EqumentSensorListVO;
import com.snszyk.sidas.basic.vo.SensorInstanceVO;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 传感器实例表 服务类
 *
 * <AUTHOR>
 */
public interface ISensorInstanceService extends BaseService<SensorInstance> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<SensorInstanceDTO> page(IPage<SensorInstanceDTO> page, SensorInstanceVO vo);

	/**
	 * 获取测点绑定的传感器实例列表
	 * @param monitorId 测点id
	 * @return
	 */
	List<SensorInstanceDTO> listByMonitorId(Long monitorId);

	/**
	 * 获取sensorCode同类型的传感器编码列表
	 * @param sensorCode 传感器编码
	 * @param equipmentIdList 设备id列表
	 * @return
	 */
    List<SensorInstance> getSameTypeSensorCodeList(String sensorCode, List<Long> equipmentIdList);

	/**
	 * 根据设备id 解绑 传感器实例
	 * @param equipmentId 设备id
	 * @return
	 */
	int unbindByEquipment(Long equipmentId);

	/**
	 * 根据测点id 解绑 传感器实例
	 * @param monitorIdList
	 * @return
	 */
	int unbindByMonitor(List<Long> monitorIdList);

	/**
	 * 根据测点id获取传感器实例列表
	 * @param monitorIdList 测点id
	 * @return
	 */
	List<SensorInstanceDTO> querySensorInfos(List<Long> monitorIdList);

	/**
	 * 绑定采集站
	 * @param sensorCodeList 传感器编码列表
	 * @param stationId 采集站id
	 * @return
	 */
	int bindCollectionStation(List<String> sensorCodeList, Long stationId);

	/**
	 * 解绑采集站
	 * @param stationId 采集站id
	 * @return
	 */
	int unbindCollectionStation(List<Long> stationId);

	/**
	 * 根据唯一识别码获取传感器实例详情
	 * @param code 唯一识别码
	 * @return
	 */
	SensorInstanceDTO detailByCode(String code);

	/**
	 * 根据传感器实例id解绑
	 * @param sensorInstanceId 传感器实例id
	 * @return
	 */
	int unbindBySensorInstance(Long sensorInstanceId);

	/**
	 * 门户端-分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<SensorInstanceDTO> portalPage(IPage<SensorInstanceDTO> page, SensorInstanceVO vo);

	/**
	 * 导出Excel
	 *
	 * @param sensorInstance
	 * @param response
	 */
	void exportExcel(SensorInstanceVO sensorInstance, HttpServletResponse response);

	/**
	 * 导出传感器实例导入模板Excel
	 *
	 * @param response
	 */
	void exportInstanceBindTemplate(HttpServletResponse response);

	/**
	 * 导入传感器实例
	 *
	 * @param data 传感器实例列表
	 * @param isCovered 是否覆盖 - 暂时忽略
	 */
	void importSensorInstance(List<SensorInstanceBindExcelDTO> data, boolean isCovered);

	/**
	 * 测点绑定传感器实例列表
	 * @param monitorId 测点id
	 * @param sensorInstanceList 传感器实例
	 * @return
	 */
	boolean bindSensorInstance(Long monitorId, List<SensorInstanceVO> sensorInstanceList);

	/**
	 * 根据传感器编码删除报警门限
	 *
	 * @param sensorCode
	 * @return
	 */
	boolean removeAlarmThresholdBySensorCode(String sensorCode);

	/**
	 * 根据设备地点查询设备及传感顺路信息
	 * @param vo
	 * @return
	 */

	List<EqumentSensorListDTO> equmentSensorList(EqumentSensorListVO vo);

	/**
	 * 查询设备安装的传感器
	 * @param equipmentId
	 * @return
	 */
	List<EquipmentSensorDTO> equipmentSensor(@Param("equipmentId") Long equipmentId);

	/**
	 * 停机线配置
	 */
	String HALT_LINE = "%s%sHALT-LINE";
}
