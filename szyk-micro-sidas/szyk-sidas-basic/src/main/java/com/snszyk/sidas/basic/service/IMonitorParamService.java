/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.sidas.basic.entity.MonitorParam;
import com.snszyk.sidas.basic.vo.MonitorParamVO;

/**
 * 设备测点参数配置表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface IMonitorParamService extends IService<MonitorParam> {

	/**
	 * 提交
	 *
	 * @param monitorParam
	 * @return
	 */
	boolean submit(MonitorParamVO monitorParam);

	/**
	 * 详情
	 *
	 * @param monitorId
	 * @return
	 */
	MonitorParamVO detail(Long monitorId);

}
