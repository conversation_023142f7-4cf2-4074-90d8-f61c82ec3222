/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.sidas.basic.dto.MonitorDTO;
import com.snszyk.sidas.basic.dto.MonitorThresholdDTO;
import com.snszyk.sidas.basic.dto.WaveDTO;
import com.snszyk.sidas.basic.entity.Monitor;
import com.snszyk.sidas.basic.vo.MonitorVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备测点表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
public interface MonitorMapper extends BaseMapper<Monitor> {

	/**
	 * 分页
	 *
	 * @param page
	 * @param monitor
	 * @return
	 */
	List<MonitorDTO> page(IPage page, @Param("monitor") MonitorVO monitor);

	/**
	 * 用于3D模型配置的分页
	 *
	 * @param page
	 * @param monitor
	 * @return
	 */
	List<MonitorDTO> monitorPage(IPage page, @Param("monitor") MonitorVO monitor);

	/**
	 * 根据设备id物理删除
	 *
	 * @param equipmentId
	 * @return
	 */
	Integer removeAll(@Param("equipmentId") Long equipmentId);

	/**
	 * 根据ids物理删除
	 *
	 * @param ids
	 * @return
	 */
	Integer removeByIds(@Param("list") List<Long> ids);

	/**
	 * 根据传感器编码查询测点
	 *
	 * @param sensorCode
	 * @return
	 */
	MonitorThresholdDTO queryBySensorCode(@Param("sensorCode") String sensorCode);

	/**
	 * 监测设备测点数量
	 *
	 * @param path
	 * @return
	 */
	List<MonitorDTO> detectMonitors(@Param("path") String path);

    Integer getMaxSort();

	/**
	 * 机理模型应用部位分页
	 *
	 * @param page
	 * @param monitor
	 * @return
	 */
	List<MonitorDTO> modelMonitorPage(IPage page, @Param("monitor") MonitorVO monitor);

	/**
	 * 创建传感器数据表
	 * @param tableName 表名
	 * @return
	 */
    int createSensorDataTable(@Param("tableName") String tableName);

	/**
	 * 获取测点波形
	 * @param monitorId 测点id
	 * @return
	 */
	List<WaveDTO> waveList(@Param("monitorId") Long monitorId);
}
