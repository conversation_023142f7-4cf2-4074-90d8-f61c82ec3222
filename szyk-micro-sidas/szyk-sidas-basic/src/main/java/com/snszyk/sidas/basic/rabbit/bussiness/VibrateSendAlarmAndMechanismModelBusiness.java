package com.snszyk.sidas.basic.rabbit.bussiness;

import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.basic.enums.InvalidEnum;
import com.snszyk.sidas.basic.enums.SampledDataTypeEnum;
import com.snszyk.sidas.basic.feign.PythonServerFeign;
import com.snszyk.sidas.basic.rabbit.handler.Command;
import com.snszyk.sidas.basic.rabbit.handler.MessageBean;
import com.snszyk.sidas.basic.vo.SensorDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 2、波形类型（加速度、速度、位移、温度）保存数据后，发送报警消息 & 调用机理模型。
 * <AUTHOR>
 */
@Slf4j
@Component
public class VibrateSendAlarmAndMechanismModelBusiness extends AbstractBusiness {

	@Resource
	private RabbitTemplate rabbitTemplate;
	@Resource
	private PythonServerFeign pythonServerFeign;

	@Override
	public String getCommand() {
		return Command.VIBRATE_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);

		SensorDataVO sensorDataVO = message.getSensorDataVO();
		boolean flag = sensorDataVO != null && !InvalidEnum.INVALID.getCode().equals(sensorDataVO.getInvalid())
			&& new BigDecimal(message.getValue()).compareTo(message.getHaltLine()) > 0;
		if(!flag){
			log.warn("VibrateSendAlarmBusiness.business() - 异常的传感器数据，不发送报警！sensorDataId = {}, monitorId = {}",
				message.getSensorDataId(), message.getMonitorId());
			return;
		}
		// 停机数据过滤
		log.debug("发送报警MQ消息给报警检测：sensorDataId = {}, monitorId = {}。",
			message.getSensorDataId(), message.getMonitorId());
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM, sensorDataVO);

		if(message.getWave() == null){
			return;
		}
		// 加速度、速度、位移的调用机理模型处理
		if (SampledDataTypeEnum.ACCELERATION.getCode().equals(message.getType())
			|| SampledDataTypeEnum.VELOCITY.getCode().equals(message.getType())
			|| SampledDataTypeEnum.DISPLACEMENT.getCode().equals(message.getType())) {
			if(Func.isNotEmpty(message.getWave())){
				log.debug("停机线：{}######是否有波形：{}######时域文件：{}", message.getHaltLine(), sensorDataVO.getHasWaveData(), sensorDataVO.getWaveformUrl());
				log.debug("调用机理模型：waveId = {}，originTime = {}", sensorDataVO.getWaveId(), DateUtil.format(sensorDataVO.getOriginTime(), DateUtil.PATTERN_DATETIME));
				pythonServerFeign.mechanismModel(DateUtil.format(sensorDataVO.getOriginTime(), DateUtil.PATTERN_DATETIME),
					String.valueOf(sensorDataVO.getWaveId()),
					PythonServerFeign.SEND_ALARM_YES,
					String.valueOf(sensorDataVO.getMonitorId()));
			}
		}
	}

}
