<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.basic.mapper.EquipmentAiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentAiResultMap" type="com.snszyk.sidas.basic.entity.EquipmentAi">
        <result column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="sensor_id" property="sensorId"/>
        <result column="sensor_code" property="sensorCode"/>
        <result column="ai_model_id" property="aiModelId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="equipmentAiDTOResultMap" type="com.snszyk.sidas.basic.dto.EquipmentAiDTO">
        <id column="id" property="id"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="path_name" property="pathName"/>
        <result column="ai_model_id" property="aiModelId"/>
        <result column="model_name" property="modelName"/>
        <result column="model_params" property="modelParams"/>
        <result column="model_version" property="modelVersion"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>


    <select id="equipmentAiPage" resultMap="equipmentAiDTOResultMap">
        SELECT
            monitor.id,
            monitor.`name` AS monitor_name,
            monitor.path_name,
            any_value ( ai.monitor_id ) AS monitor_id,
            any_value ( ai.ai_model_id ) AS ai_model_id,
            any_value ( model.`name` ) AS model_name,
            any_value ( model.params ) AS model_params,
            any_value ( model.version ) AS model_version,
            ( SELECT CASE WHEN count( 1 ) > 1 THEN 1 ELSE 0 END FROM eolm_equipment_ai WHERE monitor_id = monitor.id ) AS "has_children"
        FROM
            eolm_monitor monitor
        LEFT JOIN eolm_equipment_ai ai ON ai.monitor_id = monitor.id
        LEFT JOIN eolm_ai_model model ON model.id = ai.ai_model_id
        WHERE
            monitor.is_deleted = 0
        <if test="equipmentAi.parentId!=null">
            AND monitor.path like concat(concat('%', #{equipmentAi.parentId}), '%')
        </if>
        GROUP BY
            monitor.id
        ORDER BY
            monitor.id
    </select>

    <select id="getChildrenList" resultMap="equipmentAiDTOResultMap">
        SELECT
            ai.ai_model_id as id,
            ai.monitor_id,
            model.`name` as model_name,
            model.params as model_params,
            model.version as model_version
        FROM
            eolm_equipment_ai ai
        LEFT JOIN eolm_ai_model model on model.id = ai.ai_model_id
        WHERE ai.monitor_id = #{monitorId}
          AND ai.ai_model_id != #{modelId}
        ORDER BY
            model.id
    </select>

    <delete id="removeByEquipment">
        delete from eolm_equipment_ai where equipment_id = #{equipmentId}
    </delete>

    <delete id="removeByMonitor">
        delete from eolm_equipment_ai where monitor_id = #{monitorId}
    </delete>

    <delete id="removeEquipmentAi">
        delete from eolm_equipment_ai where equipment_id = #{equipmentId} and ai_model_id = #{aiModelId}
    </delete>

    <delete id="removeBySensorCode">
        delete from eolm_equipment_ai where sensor_code = #{sensorCode}
    </delete>

    <select id="systemConfigStat" resultType="java.lang.Integer">
        SELECT count(*) FROM `eolm_equipment_ai` ai left join eolm_equipment equipment on equipment.id = ai.equipment_id where equipment.tenant_id=#{tenantId} group by ai_model_id
    </select>

</mapper>
