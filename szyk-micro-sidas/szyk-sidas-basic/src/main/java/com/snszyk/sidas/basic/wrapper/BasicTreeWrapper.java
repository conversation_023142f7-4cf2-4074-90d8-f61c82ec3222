/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.basic.cache.BizCache;
import com.snszyk.sidas.basic.entity.BasicTree;
import com.snszyk.sidas.basic.vo.BasicTreeVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.enums.DictBizEnum;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 基础树表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public class BasicTreeWrapper extends BaseEntityWrapper<BasicTree, BasicTreeVO> {

	public static BasicTreeWrapper build() {
		return new BasicTreeWrapper();
 	}

	@Override
	public BasicTreeVO entityVO(BasicTree tree) {
		BasicTreeVO treeVO = Objects.requireNonNull(BeanUtil.copy(tree, BasicTreeVO.class));
		if (Func.equals(tree.getParentId(), SzykConstant.TOP_PARENT_ID)) {
			treeVO.setParentName(SzykConstant.TOP_PARENT_NAME);
		} else {
			BasicTree parent = BizCache.getBasicNode(tree.getParentId());
			treeVO.setParentName(parent.getNodeName());
		}
		String category = DictBizCache.getValue(DictBizEnum.DEVICE_CATEGORY, tree.getNodeCategory());
		treeVO.setNodeCategoryName(category);
		return treeVO;
	}

	public List<BasicTreeVO> listNodeVO(List<BasicTree> list) {
		List<BasicTreeVO> collect = list.stream().map(tree -> {
			BasicTreeVO treeVO = BeanUtil.copy(tree, BasicTreeVO.class);
			String category = DictBizCache.getValue(DictBizEnum.DEVICE_CATEGORY, tree.getNodeCategory());
			Objects.requireNonNull(treeVO).setNodeCategoryName(category);
			return treeVO;
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

	public List<BasicTreeVO> listNodeLazyVO(List<BasicTreeVO> list) {
		List<BasicTreeVO> collect = list.stream().peek(tree -> {
			String category = DictBizCache.getValue(DictBizEnum.DEVICE_CATEGORY, tree.getNodeCategory());
			Objects.requireNonNull(tree).setNodeCategoryName(category);
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
