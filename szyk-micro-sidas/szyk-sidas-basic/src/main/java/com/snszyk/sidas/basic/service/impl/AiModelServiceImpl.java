/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.sidas.basic.dto.AiModelDTO;
import com.snszyk.sidas.basic.entity.AiModel;
import com.snszyk.sidas.basic.entity.Equipment;
import com.snszyk.sidas.basic.entity.EquipmentAi;
import com.snszyk.sidas.basic.mapper.AiModelMapper;
import com.snszyk.sidas.basic.service.IAiModelService;
import com.snszyk.sidas.basic.service.IEquipmentAiService;
import com.snszyk.sidas.basic.service.IEquipmentService;
import com.snszyk.sidas.basic.vo.AiModelVO;
import com.snszyk.sidas.basic.wrapper.AiModelWrapper;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AI模型表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Service
@AllArgsConstructor
public class AiModelServiceImpl extends BaseServiceImpl<AiModelMapper, AiModel> implements IAiModelService {

	private final IEquipmentService equipmentService;
	private final IEquipmentAiService equipmentAiService;

	private AiModelMapper aiModelMapper;

	@Override
	public IPage<AiModelDTO> page(IPage<AiModelDTO> page, AiModelVO aiModel) {
		return page.setRecords(baseMapper.page(page, aiModel));
	}

	@Override
	public boolean submit(AiModelVO vo) {
		AiModel entity;
		if (Func.isNotEmpty(vo.getId())) {
			Integer count = equipmentAiService.count(Wrappers.<EquipmentAi>query().lambda().eq(EquipmentAi::getAiModelId, vo.getId()));
			if(vo.getStatus() == 1 && count > 0){
				throw new ServiceException("当前模型被设备应用，不能停用！");
			}
			entity = this.getOne(Wrappers.<AiModel>query().lambda().eq(AiModel::getName, vo.getName()).ne(AiModel::getId, vo.getId()));
		} else {
			entity = this.getOne(Wrappers.<AiModel>query().lambda().eq(AiModel::getName, vo.getName()));
		}
		if (Func.isNotEmpty(entity)) {
			throw new ServiceException("模型名称：" + vo.getName() + "已存在");
		}
		AiModel aiModel = Objects.requireNonNull(BeanUtil.copy(vo, AiModel.class));
		return this.saveOrUpdate(aiModel);
	}

	@Override
	public DelResultVO checkAndRemoveAiModel(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.forEach(id -> {
			//查询机构信息
			AiModel aiModel = this.getById(id);
			//如果模型不存在，直接失败返回
			if (aiModel == null) {
				resultVO.getDetailVOList().add(new DelDetailVO(id.toString(),Boolean.FALSE, "模型不存在"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在设备引用，不允许删除
			List<EquipmentAi> refs = equipmentAiService.list(Wrappers.<EquipmentAi>query().lambda().eq(EquipmentAi::getAiModelId, Long.toString(id)));
			if (CollectionUtil.isEmpty(refs)) {
				//如果无设备引用，则删除此模型
				this.removeById(id);
				resultVO.getDetailVOList().add(new DelDetailVO(aiModel.getName(),Boolean.TRUE, "删除成功"));
				//成功次数+1
				resultVO.setSuccessNumber(resultVO.getSuccessNumber() + 1);
			} else {
				//存在设备引用不允许删除，收集引用的设备名称放入失败提示信息
				Set<String> refNameSet = refs.stream().map(ref -> equipmentService.getOne(Wrappers.<Equipment>query().lambda()
					.eq(Equipment::getId, ref.getEquipmentId()).select(Equipment::getName)).getName()).collect(Collectors.toSet());
				resultVO.getDetailVOList().add(new DelDetailVO(aiModel.getName(),Boolean.FALSE, "模型信息被设备应用，无法删除，具体如下：\r\n"
					+ StringUtil.collectionToDelimitedString(refNameSet, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}

	@Override
	public List<AiModelVO> equipmentAiModelList(Long equipmentId) {
		List<EquipmentAi> list = equipmentAiService.list(Wrappers.<EquipmentAi>query().lambda().eq(EquipmentAi::getEquipmentId, equipmentId));
		List<Long> aiModelIds = list.stream()
			.map(EquipmentAi::getAiModelId)
			.distinct()
			.collect(Collectors.toList());
		return AiModelWrapper.build().listVO(this.listByIds(aiModelIds));
	}

	@Override
	public boolean apply(Long id, Long equipmentId) {
		if (Func.isNotEmpty(equipmentId)) {
			Equipment equipment = equipmentService.getById(equipmentId);
			if (Func.isNotEmpty(equipment)) {
				AiModel aiModel = this.getById(id);
				if (Func.isNotEmpty(aiModel)) {
					aiModel.setEquipmentId(equipmentId);
					return this.updateById(aiModel);
				}
			}
		}
		return false;
	}

	@Override
	public List<AiModelDTO> queryAiModelParams(String sensorCode) {
		return aiModelMapper.queryAiModelParams(sensorCode);
	}

}
