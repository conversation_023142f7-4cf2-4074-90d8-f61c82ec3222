package com.snszyk.sidas.basic.rabbit.station;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.sidas.basic.dto.SensorInstanceDTO;
import com.snszyk.sidas.basic.dto.SensorOfflineDTO;
import com.snszyk.sidas.basic.entity.CollectionStation;
import com.snszyk.sidas.basic.entity.CollectionStationChannel;
import com.snszyk.sidas.basic.service.ICollectionStationChannelService;
import com.snszyk.sidas.basic.service.ICollectionStationService;
import com.snszyk.sidas.basic.service.ISensorInstanceService;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.feign.IUserSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SendWarinBusiness {
	@Resource
	private ISensorInstanceService sensorInstanceService;
	@Resource
	private ISysClient sysClient;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IUserSearchClient userSearchClient;
	@Resource
	private RabbitTemplate rabbitTemplate;

	@Resource
	private ICollectionStationChannelService channelService;
	@Resource
	private ICollectionStationService stationService;
	public void updateStationOffline(String code) {

		boolean update = stationService.update(
			new UpdateWrapper<CollectionStation>()
				.lambda()
				.set(CollectionStation::getOnline, 0)
				.eq(CollectionStation::getId, code)
		);
		log.info("采集站离线更新:{}", update);
	}

	public void updateChannelOffline(String sensorCode){
		boolean update = channelService.update(
			new UpdateWrapper<CollectionStationChannel>()
				.lambda()
				.set(CollectionStationChannel::getOnline, 0)
				.eq(CollectionStationChannel::getSensorCode, sensorCode)
		);
		log.info("通道离线更新:{}", update);
	}

	public void sendArm(String code){
		CollectionStation station = stationService.getById(code);
		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("SiDAs");
		messageVo.setTitle(MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getMessage() + "通知");
		//content改为json字符串
		messageVo.setContent(JSONObject.toJSONString(station));
		messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		messageVo.setBizType(MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getCode());
		messageVo.setBizId(String.valueOf(station.getId()));
		messageVo.setSender("SiDAs");
		messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		R<List<User>> allUserResult = userSearchClient.listAllUser(station.getTenantId());
		if (allUserResult.isSuccess() && CollectionUtil.isNotEmpty(allUserResult.getData())) {
			List<ReceiverInfoVo.UserVo> userVoList = allUserResult.getData().stream().map(user -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(user.getId());
				userVo.setRealName(user.getRealName());
				return userVo;
			}).collect(Collectors.toList());
			receiverInfoVo.setUserList(userVoList);
		}
		messageVo.setReceiverInfoVo(receiverInfoVo);
		log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 接收人：{}", JSONObject.toJSONString(receiverInfoVo));
		R messageResult = messageClient.pushMessage(messageVo);
		log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 结果：{}", JSONObject.toJSONString(messageResult));
	}

	public void sendAlarm(String sensorCode) {
		// 查询传感器参数id
		// 发送离线通知
		R<List<User>> allUserResult = userSearchClient.listAllUser(AuthUtil.getTenantId());
		if (allUserResult.isSuccess() && Func.isNotEmpty(allUserResult.getData())) {
			//传感器实例（测点路径）
			SensorInstanceDTO sensorInstanceDTO = sensorInstanceService.detailByCode(sensorCode);
			//发送采集站离线待办消息
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SiDAs");
			messageVo.setTitle(MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知");
			SensorOfflineDTO messageContent = new SensorOfflineDTO()
				.setPathName(sensorInstanceDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH))
				.setSensorCode(sensorInstanceDTO.getCode());
			messageVo.setContent(JSONObject.toJSONString(messageContent));
			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			messageVo.setBizType(MessageBizTypeEnum.SENSOR_OFFLINE.getCode());
			messageVo.setBizId(sensorInstanceDTO.getId().toString());
			messageVo.setSender("SiDAs");
			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			List<ReceiverInfoVo.UserVo> userVoList = allUserResult.getData().stream().map(user -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(user.getId());
				userVo.setRealName(user.getRealName());
				return userVo;
			}).collect(Collectors.toList());
			receiverInfoVo.setUserList(userVoList);
			messageVo.setReceiverInfoVo(receiverInfoVo);
			log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 接收人：{}", JSONObject.toJSONString(userVoList));
			R messageResult = messageClient.pushMessage(messageVo);
			log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 结果：{}", JSONObject.toJSONString(messageResult));
			// 发送钉钉消息
			R<Tenant> tenantInfo = sysClient.getTenant(AuthUtil.getTenantId());
			log.info(MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "-发送钉钉消息：==================={}", sensorInstanceDTO.getCode());
			DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenantInfo.getData().getTenantId(),
				tenantInfo.getData().getTenantName(), sensorInstanceDTO.getCode(),
				MessageBizTypeEnum.SENSOR_OFFLINE.getCode(), JSONUtil.toJsonStr(sensorInstanceDTO));
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
				EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
		} else {
			log.warn("获取用户列表失败！code = {}, msg = {}.", allUserResult.getCode(), allUserResult.getMsg());
		}
	}
}
