<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.sidas.basic.mapper.WaveMapper">

    <resultMap id="waveDTOResultMap" type="com.snszyk.sidas.basic.dto.WaveDTO">
        <result column="id" property="id"/>
        <result column="wave_name" property="waveName"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="path_name" property="pathName"/>
        <result column="equipment_type" property="equipmentType"/>
    </resultMap>


    <select id="queryWave" resultMap="waveDTOResultMap">
        SELECT
            w.id,
            w.wave_name,
            m.id AS monitor_id,
            m.`name` AS monitor_name,
            m.equipment_type,
            m.path,
            m.path_name
        FROM
            sidas_wave w
        LEFT JOIN eolm_monitor m ON m.id = w.monitor_id
        WHERE w.unbind = 0 AND m.is_deleted = 0
        <if test="wave.parentId != null">
            AND m.path LIKE CONCAT('%', #{wave.parentId}, '%')
        </if>
        <if test="wave.equipmentType != null">
            AND m.equipment_type = #{wave.equipmentType}
        </if>
        <if test="wave.sampleDataType != null and wave.sampleDataType != ''">
            AND w.sample_data_type = #{wave.sampleDataType}
        </if>
    </select>

    <update id="removeAlarmThresholdByWave">
        UPDATE eolm_alarm_threshold
        SET is_deleted = 1 WHERE wave_id in
            <foreach collection="list" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </update>

    <update id="unbindWaveByIds">
        UPDATE sidas_wave
        SET unbind = 1 WHERE id in
        <foreach collection="list" item="id" index="i" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

</mapper>
