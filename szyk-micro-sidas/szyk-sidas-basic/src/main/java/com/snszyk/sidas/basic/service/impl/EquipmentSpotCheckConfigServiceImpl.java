/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.sidas.basic.dto.EquipmentSpotCheckConfigDTO;
import com.snszyk.sidas.basic.entity.EquipmentSpotCheckConfig;
import com.snszyk.sidas.basic.mapper.EquipmentSpotCheckConfigMapper;
import com.snszyk.sidas.basic.service.IEquipmentSpotCheckConfigService;
import com.snszyk.sidas.basic.vo.EquipmentSpotCheckConfigVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备点检计划配置表 服务实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class EquipmentSpotCheckConfigServiceImpl extends BaseServiceImpl<EquipmentSpotCheckConfigMapper, EquipmentSpotCheckConfig>
	implements IEquipmentSpotCheckConfigService {

	@Override
	public int removeByEquipmentId(Long equipmentId) {
		return baseMapper.removeByEquipmentId(equipmentId);
	}

	@Override
	public List<EquipmentSpotCheckConfigDTO> selectConfigPage(IPage<EquipmentSpotCheckConfigDTO> page, EquipmentSpotCheckConfigVO configVO) {
		return baseMapper.selectConfigPage(page, configVO);
	}
}
