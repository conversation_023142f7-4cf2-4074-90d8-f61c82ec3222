<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.basic.mapper.DeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceResultMap" type="com.snszyk.sidas.basic.entity.Device">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="coordinate" property="coordinate"/>
        <result column="longitude_latitude" property="longitudeLatitude"/>
        <result column="path" property="path"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="deviceDTOResultMap" type="com.snszyk.sidas.basic.dto.DeviceDTO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="coordinate" property="coordinate"/>
        <result column="longitude_latitude" property="longitudeLatitude"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="level" property="level"/>
        <result column="sort" property="sort"/>
    </resultMap>


    <select id="getByParentId" resultMap="deviceDTOResultMap">
        SELECT
            device.*,
            tree.path,
            tree.path_name,
            tree.node_level AS level
        FROM
            eolm_device device
        LEFT JOIN sidas_basic_tree tree ON tree.id = device.id
        WHERE device.is_deleted = 0 AND (device.`type` = 1 or device.`type` = 3)
          AND device.parent_id = #{id}
    </select>

    <select id="getByLevel" resultMap="deviceDTOResultMap">
        SELECT
            device.*,
            tree.path,
            tree.path_name,
            tree.node_level AS level
        FROM
            eolm_device device
        LEFT JOIN sidas_basic_tree tree ON tree.id = device.id
        WHERE device.is_deleted = 0 AND (device.`type` = 1 or device.`type` = 3) AND tree.node_level = #{level}
    </select>

    <!--查询门户区域点位视图列表-->
    <select id="deviceView" resultType="com.snszyk.sidas.basic.dto.DeviceCoordinateSubDTO">
        select
            device.id,
            device.tenant_id as tenantId,
            device.parent_id AS parentId,
            device.code,
            device.name,
            device.ancestors,
            device.path,
            device.path_name AS pathName,
            device.image,
            device.coordinate,
            device.category,
            ( SELECT CASE WHEN count( 1 ) > 0 THEN 1 ELSE 0 END FROM eolm_equipment WHERE device_id = device.id AND is_deleted = 0 ) AS hasDevice
        from eolm_device device
        where device.parent_id = #{id} and is_deleted = 0 AND (device.`type` = 1 or device.`type` = 3)
    </select>

    <select id="selectMaxSort" resultType="java.lang.Integer">
        select max(sort) from eolm_device
    </select>

    <select id="listSecondLevelDevice" resultType="com.snszyk.sidas.basic.entity.Device">
        SELECT *
        FROM eolm_device
        <where>
            is_deleted = 0 AND (`type` = 1 or `type` = 3)
            AND parent_id = (SELECT id FROM eolm_device
            <where>
                is_deleted = 0 AND (`type` = 1 or `type` = 3) AND parent_id = 0
                <if test="tenantId!= null and tenantId!= ''">
                        AND tenant_id = #{tenantId}
                </if>
            </where>
            )
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </select>

</mapper>
