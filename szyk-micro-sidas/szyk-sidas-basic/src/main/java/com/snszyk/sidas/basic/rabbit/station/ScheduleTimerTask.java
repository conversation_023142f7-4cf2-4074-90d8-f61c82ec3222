package com.snszyk.sidas.basic.rabbit.station;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.TimerTask;

@Data
@Slf4j
public class ScheduleTimerTask extends TimerTask {

	//是否为采集站
	private Boolean station;
	private String code;
	private SendWarinBusiness business;
	public ScheduleTimerTask(String code,SendWarinBusiness business, Boolean station){
		this.business = business;
		this.code = code;
		this.station = station;
	}
	@Override
	public void run() {
		if(station){
			log.info("设置采集站为离线状态，并发送报警信息");
			business.updateStationOffline(code);
			business.sendArm(code);
		}else {
			log.info("设置通道为离线状态，并发送报警信息");
			business.updateChannelOffline(code);
			business.sendAlarm(code);
		}

	}
}
