<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.diagnosis.mapper.AbnormalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="abnormalResultMap" type="com.snszyk.sidas.diagnosis.entity.Abnormal">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_reason" property="abnormalReason"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="first_time" property="firstTime"/>
        <result column="last_time" property="lastTime"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="close_reason" property="closeReason"/>
        <result column="close_time" property="closeTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="abnormalDTOResultMap" type="com.snszyk.sidas.diagnosis.dto.AbnormalDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_reason" property="abnormalReason"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="first_time" property="firstTime"/>
        <result column="last_time" property="lastTime"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="close_reason" property="closeReason"/>
        <result column="close_time" property="closeTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="page" resultMap="abnormalDTOResultMap">
        select * from eolm_abnormal where 1=1
        <if test="abnormal.status!=null">
            and status= #{abnormal.status}
        </if>
        <if test="abnormal.equipmentId!=null">
            and equipment_id = #{abnormal.equipmentId}
        </if>
        <if test="abnormal.startDate!=null">
            and create_time <![CDATA[ >= ]]> #{abnormal.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="abnormal.endDate!=null">
            and create_time <![CDATA[ <= ]]> #{abnormal.endDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

<!--    <select id="abnormalTypeTotal" resultType="com.snszyk.sidas.diagnosis.dto.AbnormalTypeDTO">-->
<!--        SELECT GROUP_CONCAT(abnormal_type)abnormalType,GROUP_CONCAT(ct) ct FROM (-->
<!--                 SELECT abnormal_type,COUNT(*) ct FROM `eolm_abnormal` a JOIN-->
<!--                 `eolm_abnormal_record` b ON a.id=b.abnormal_id WHERE YEAR(a.first_time)=YEAR(NOW())-->
<!--        AND a.equipment_id=#{id} GROUP BY b.abnormal_type-->
<!--    ) a-->

<!--    </select>-->
</mapper>
