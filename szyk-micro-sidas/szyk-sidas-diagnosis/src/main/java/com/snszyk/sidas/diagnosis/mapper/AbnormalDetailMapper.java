/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.mapper;

import com.snszyk.sidas.diagnosis.entity.AbnormalDetail;
import com.snszyk.sidas.diagnosis.vo.AbnormalDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 设备异常详情表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface AbnormalDetailMapper extends BaseMapper<AbnormalDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param abnormalDetail
	 * @return
	 */
	List<AbnormalDetailVO> page(IPage page, AbnormalDetailVO abnormalDetail);

}
