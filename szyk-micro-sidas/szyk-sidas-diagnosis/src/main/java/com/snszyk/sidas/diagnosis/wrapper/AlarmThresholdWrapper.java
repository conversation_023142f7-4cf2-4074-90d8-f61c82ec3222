/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.sidas.diagnosis.entity.AlarmThreshold;
import com.snszyk.sidas.diagnosis.vo.AlarmThresholdVO;

import java.util.Objects;

/**
 * 报警门限表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public class AlarmThresholdWrapper extends BaseEntityWrapper<AlarmThreshold, AlarmThresholdVO> {

	public static AlarmThresholdWrapper build() {
		return new AlarmThresholdWrapper();
 	}

	@Override
	public AlarmThresholdVO entityVO(AlarmThreshold alarmThreshold) {
		AlarmThresholdVO alarmThresholdVO = Objects.requireNonNull(BeanUtil.copy(alarmThreshold, AlarmThresholdVO.class));

		//User createUser = UserCache.getUser(alarmThreshold.getCreateUser());
		//User updateUser = UserCache.getUser(alarmThreshold.getUpdateUser());
		//alarmThresholdVO.setCreateUserName(createUser.getName());
		//alarmThresholdVO.setUpdateUserName(updateUser.getName());

		return alarmThresholdVO;
	}

}
