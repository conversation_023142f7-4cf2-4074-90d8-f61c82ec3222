/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.sidas.diagnosis.entity.AbnormalDetail;
import com.snszyk.sidas.diagnosis.vo.AbnormalDetailVO;

import java.util.Objects;

/**
 * 设备异常详情表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public class AbnormalDetailWrapper extends BaseEntityWrapper<AbnormalDetail, AbnormalDetailVO> {

	public static AbnormalDetailWrapper build() {
		return new AbnormalDetailWrapper();
 	}

	@Override
	public AbnormalDetailVO entityVO(AbnormalDetail abnormalDetail) {
		AbnormalDetailVO abnormalDetailVO = Objects.requireNonNull(BeanUtil.copy(abnormalDetail, AbnormalDetailVO.class));

		//User createUser = UserCache.getUser(abnormalDetail.getCreateUser());
		//User updateUser = UserCache.getUser(abnormalDetail.getUpdateUser());
		//abnormalDetailVO.setCreateUserName(createUser.getName());
		//abnormalDetailVO.setUpdateUserName(updateUser.getName());

		return abnormalDetailVO;
	}

}
