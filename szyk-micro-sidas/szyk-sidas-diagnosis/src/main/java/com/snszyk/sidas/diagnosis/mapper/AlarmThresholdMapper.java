/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.sidas.diagnosis.dto.AlarmThresholdDTO;
import com.snszyk.sidas.diagnosis.entity.AlarmThreshold;
import com.snszyk.sidas.diagnosis.vo.AlarmThresholdVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报警门限表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface AlarmThresholdMapper extends BaseMapper<AlarmThreshold> {

	/**
	 * 振动指标门限分页
	 *
	 * @param page
	 * @param alarmThreshold
	 * @return
	 */
	List<AlarmThresholdDTO> vibrationQuotaPage(IPage page, @Param("alarmThreshold") AlarmThresholdVO alarmThreshold);

	/**
	 * 应力波指标门限分页
	 *
	 * @param page
	 * @param alarmThreshold
	 * @return
	 */
	List<AlarmThresholdDTO> stressQuotaPage(IPage page, @Param("alarmThreshold") AlarmThresholdVO alarmThreshold);

	/**
	 * 温度指标门限分页
	 *
	 * @param page
	 * @param alarmThreshold
	 * @return
	 */
	List<AlarmThresholdDTO> nonVibrationQuotaPage(IPage page, @Param("alarmThreshold") AlarmThresholdVO alarmThreshold);

}
