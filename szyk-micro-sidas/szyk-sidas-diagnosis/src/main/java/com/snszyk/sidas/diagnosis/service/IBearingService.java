/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.diagnosis.dto.BearingDTO;
import com.snszyk.sidas.diagnosis.dto.BearingImportDTO;
import com.snszyk.sidas.diagnosis.dto.MonitorBearingDTO;
import com.snszyk.sidas.diagnosis.entity.Bearing;
import com.snszyk.sidas.diagnosis.vo.BearingVO;
import com.snszyk.system.vo.DelResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 轴承库 服务类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface IBearingService extends BaseService<Bearing> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bearing
	 * @return
	 */
	IPage<BearingDTO> page(IPage<BearingDTO> page, BearingVO bearing);

	/**
	 * 导出
	 *
	 * @param bearing
	 * @param response
	 */
	void exportExcel(BearingVO bearing, HttpServletResponse response);

	/**
	 * 轴承id列表
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemove(List<Long> ids);

	/**
	 * 导入轴承数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	boolean importBearing(List<BearingImportDTO> data, Boolean isCovered);

	/**
	 * 下载导入失败轴承数据
	 *
	 * @param response
	 */
	void downloadFailExcel(HttpServletResponse response);

}
