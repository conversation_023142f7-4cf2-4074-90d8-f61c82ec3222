package com.snszyk.sidas.smart.factory;

import com.snszyk.sidas.smart.enums.DifyMessageType;
import com.snszyk.sidas.smart.model.ChatAssistantRequest;
import com.snszyk.sidas.smart.model.ChatflowRequest;
import com.snszyk.sidas.smart.model.BaseRequest;
import com.snszyk.sidas.smart.model.WorkflowRequest;
import org.springframework.stereotype.Component;

@Component
public class RequestFactory {

	public BaseRequest createRequest(DifyMessageType type) {
		switch (type) {
			case CHAT_ASSISTANT:
				return new ChatAssistantRequest();
			case CHATFLOW:
				return new ChatflowRequest();
			case WORKFLOW:
				return new WorkflowRequest();
			default:
				throw new IllegalArgumentException("Unsupported DifyMessageType: " + type);
		}
	}
}
