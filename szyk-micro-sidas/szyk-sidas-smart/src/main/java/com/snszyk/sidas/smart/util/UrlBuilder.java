package com.snszyk.sidas.smart.util;

import org.springframework.web.util.UriTemplate;

import java.util.Map;

/**
 * URL构建工具类
 * 提供安全、专业的URL构建方法，避免字符串拼接和替换的问题
 * 
 * <AUTHOR>
 */
public class UrlBuilder {

    /**
     * 使用UriTemplate构建URL
     * 
     * @param baseUrl 基础URL
     * @param pathTemplate 路径模板，如 "/messages/{message_id}/suggested"
     * @param variables 路径变量
     * @return 完整的URL
     */
    public static String buildUrl(String baseUrl, String pathTemplate, Object... variables) {
        UriTemplate uriTemplate = new UriTemplate(baseUrl + pathTemplate);
        return uriTemplate.expand(variables).toString();
    }

    /**
     * 使用UriTemplate构建URL（Map方式）
     * 
     * @param baseUrl 基础URL
     * @param pathTemplate 路径模板，如 "/conversations/{conversation_id}"
     * @param variableMap 路径变量Map
     * @return 完整的URL
     */
    public static String buildUrl(String baseUrl, String pathTemplate, Map<String, Object> variableMap) {
        UriTemplate uriTemplate = new UriTemplate(baseUrl + pathTemplate);
        return uriTemplate.expand(variableMap).toString();
    }

    /**
     * 构建Dify API URL的便捷方法
     * 
     * @param baseUrl Dify基础URL
     * @param pathTemplate 路径模板
     * @param variables 路径变量
     * @return 完整的Dify API URL
     */
    public static String buildDifyUrl(String baseUrl, String pathTemplate, Object... variables) {
        return buildUrl(baseUrl, pathTemplate, variables);
    }

    /**
     * 构建Dify API URL的便捷方法（Map方式）
     * 
     * @param baseUrl Dify基础URL
     * @param pathTemplate 路径模板
     * @param variableMap 路径变量Map
     * @return 完整的Dify API URL
     */
    public static String buildDifyUrl(String baseUrl, String pathTemplate, Map<String, Object> variableMap) {
        return buildUrl(baseUrl, pathTemplate, variableMap);
    }
}
