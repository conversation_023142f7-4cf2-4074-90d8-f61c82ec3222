package com.snszyk.sidas.smart.client;

import com.alibaba.fastjson.JSONArray;
import com.snszyk.sidas.smart.config.DifyProperties;
import com.snszyk.sidas.smart.constent.DifyConstent;
import com.snszyk.sidas.smart.util.RestTemplateUtil;
import com.snszyk.sidas.smart.util.UrlBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Dify API 客户端
 * 封装所有与Dify API交互的方法，提供类型安全和统一的错误处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyApiClient {

    private final DifyProperties difyProperties;

    /**
     * 获取会话历史记录
     * 
     * @param type API类型
     * @param userId 用户ID
     * @param limit 限制数量
     * @param lastId 最后一条记录ID
     * @param sortBy 排序方式
     * @return 会话历史记录
     */
    public JSONArray getConversationHistory(String type, String userId, Object limit, Object lastId, Object sortBy) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", userId);
        if (limit != null) {
            params.put("limit", limit);
        }
        if (lastId != null) {
            params.put("last_id", lastId);
        }
        if (sortBy != null) {
            params.put("sort_by", sortBy);
        }

        Map<String, String> headers = buildAuthHeaders(type);
        String url = difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_HISTORY;
        
        ResponseEntity<String> response = RestTemplateUtil.get(url, params, headers, String.class);
        return parseDataArray(response);
    }

    /**
     * 获取会话消息
     * 
     * @param type API类型
     * @param userId 用户ID
     * @param conversationId 会话ID
     * @param limit 限制数量
     * @param firstId 第一条记录ID
     * @return 会话消息列表
     */
    public JSONArray getConversationMessages(String type, String userId, String conversationId, Object limit, Object firstId) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", userId);
        params.put("conversation_id", conversationId);
        if (limit != null) {
            params.put("limit", limit);
        }
        if (firstId != null) {
            params.put("first_id", firstId);
        }

        Map<String, String> headers = buildAuthHeaders(type);
        String url = difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_MESSAGE;
        
        ResponseEntity<String> response = RestTemplateUtil.get(url, params, headers, String.class);
        return parseDataArray(response);
    }

    /**
     * 获取下一步建议
     * 
     * @param type API类型
     * @param userId 用户ID
     * @param messageId 消息ID
     * @return 建议列表
     */
    public JSONArray getNextStepSuggestions(String type, String userId, String messageId) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", userId);

        Map<String, String> headers = buildAuthHeaders(type);
        String url = UrlBuilder.buildDifyUrl(
            difyProperties.getBaseUrl(),
            DifyConstent.NEXT_STEP_SUGGESTION,
            messageId
        );
        
        ResponseEntity<String> response = RestTemplateUtil.get(url, params, headers, String.class);
        return parseDataArray(response);
    }

    /**
     * 删除会话
     * 
     * @param type API类型
     * @param userId 用户ID
     * @param conversationId 会话ID
     * @return 是否删除成功
     */
    public boolean deleteConversation(String type, String userId, String conversationId) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", userId);

        Map<String, String> headers = buildAuthHeaders(type);
        String url = UrlBuilder.buildDifyUrl(
            difyProperties.getBaseUrl(),
            DifyConstent.DEL_CONVERSATION,
            conversationId
        );
        
        ResponseEntity<String> response = RestTemplateUtil.get(url, params, headers, String.class);
        log.info("Dify删除会话响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), response.getBody());
        return response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 构建认证请求头
     * 
     * @param type API类型
     * @return 请求头Map
     */
    private Map<String, String> buildAuthHeaders(String type) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + difyProperties.getApiKey().get(type));
        headers.put("Content-Type", "application/json; charset=utf-8");
        return headers;
    }

    /**
     * 解析响应中的data数组
     * 
     * @param response HTTP响应
     * @return JSONArray数据
     */
    private JSONArray parseDataArray(ResponseEntity<String> response) {
        String responseBody = response.getBody();
        log.info("Dify API响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);
        
        if (responseBody != null) {
            return com.alibaba.fastjson.JSON.parseObject(responseBody).getJSONArray("data");
        }
        return new JSONArray();
    }
}
