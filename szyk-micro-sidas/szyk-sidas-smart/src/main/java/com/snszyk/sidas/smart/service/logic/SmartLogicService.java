package com.snszyk.sidas.smart.service.logic;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.smart.client.DifyApiClient;
import com.snszyk.sidas.smart.client.SseEmitterClient;
import com.snszyk.sidas.smart.config.DifyProperties;
import com.snszyk.sidas.smart.constent.DifyConstent;
import com.snszyk.sidas.smart.enums.DifyMessageType;
import com.snszyk.sidas.smart.factory.RequestFactory;
import com.snszyk.sidas.smart.model.BaseRequest;
import com.snszyk.sidas.smart.model.ChatAssistantRequest;
import com.snszyk.sidas.smart.model.ChatflowRequest;
import com.snszyk.sidas.smart.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智能服务逻辑层
 *
 * <p>负责处理与Dify AI平台的交互逻辑，包括：</p>
 * <ul>
 *   <li>智能对话请求处理</li>
 *   <li>会话历史记录管理</li>
 *   <li>会话消息查询</li>
 *   <li>下一步建议获取</li>
 *   <li>会话删除操作</li>
 * </ul>
 *
 * <p>支持多种Dify消息类型：</p>
 * <ul>
 *   <li>CHAT_ASSISTANT - 聊天助手</li>
 *   <li>CHATFLOW - 对话流程</li>
 *   <li>WORKFLOW - 工作流</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmartLogicService {

    /** 请求工厂，用于创建不同类型的Dify请求对象 */
    private final RequestFactory requestFactory;

    /** Dify配置属性，包含API密钥和基础URL等配置信息 */
    private final DifyProperties difyProperties;

    /** Dify API客户端，封装了与Dify平台的HTTP通信逻辑 */
    private final DifyApiClient difyApiClient;

	public SseEmitter processRequest(MessageVo v) {
		log.info("dify请求接口：{}", JSON.toJSONString(difyProperties));
		DifyMessageType type = DifyMessageType.getByCode(v.getType());
		BaseRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(Func.toStr(AuthUtil.getUserId()))
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()));

		// 根据类型设置特定属性
		switch (type) {
			case CHAT_ASSISTANT:
				request.setUri(DifyConstent.CHAT_ASSISTANT_PATH);
				((ChatAssistantRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case CHATFLOW:
				request.setUri(DifyConstent.CHATFLOW_PATH);
				((ChatflowRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case WORKFLOW:
				request.setUri(DifyConstent.WORKFLOW_PATH);
				if (StringUtils.isNotBlank(v.getContent())) {
					request.getInputs().put("query", v.getContent());
				}
				break;
			default:
				log.warn("Unsupported message type: {}", type);
				break;
		}

		// 发送请求并处理响应
		return SseEmitterClient.createEmitter(request);
	}

	public JSONArray chatflowHistory(Map<String, Object> map) {
		log.info("获取dify通用问答流程历史会话记录：{}", JSON.toJSONString(map));
		try {
			return difyApiClient.getConversationHistory(
				map.get("type").toString(),
				Func.toStr(AuthUtil.getUserId()),
				map.get("limit"),
				map.get("lastId"),
				map.get("sortBy")
			);
		} catch (Exception e) {
			log.error("获取Dify历史会话记录异常", e);
			throw new ServiceException("获取历史会话记录失败: " + e.getMessage());
		}
	}

	public JSONArray conversationMessage(Map<String, Object> map) {
		log.info("获取dify通用问答会话消息：{}", JSON.toJSONString(map));
		try {
			return difyApiClient.getConversationMessages(
				map.get("type").toString(),
				Func.toStr(AuthUtil.getUserId()),
				map.get("conversationId").toString(),
				map.get("limit"),
				map.get("firstId")
			);
		} catch (Exception e) {
			log.error("获取Dify会话消息异常", e);
			throw new ServiceException("获取会话消息失败: " + e.getMessage());
		}
	}

	public List<String> nextStepSuggestion(Map<String, String> map) throws IOException {
		JSONArray suggestions = difyApiClient.getNextStepSuggestions(
			map.get("type"),
			Func.toStr(AuthUtil.getUserId()),
			map.get("messageId")
		);
		return suggestions != null ? suggestions.toJavaList(String.class) : new ArrayList<>();
	}

	public boolean delConversation(Map<String, Object> map) {
		return difyApiClient.deleteConversation(
			map.get("type").toString(),
			Func.toStr(AuthUtil.getUserId()),
			map.get("conversationId").toString()
		);
	}
}
