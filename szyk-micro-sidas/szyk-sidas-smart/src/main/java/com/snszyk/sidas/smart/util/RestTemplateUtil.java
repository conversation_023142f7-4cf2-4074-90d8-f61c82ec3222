package com.snszyk.sidas.smart.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

@Slf4j
public class RestTemplateUtil {
    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应内容
     */
    public static <T> ResponseEntity<T> get(String url, Map<String, Object> params,
                                         Map<String, String> headers, Class<T> responseType) {
        // 构建URL和参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (params != null) {
            params.forEach((key, value) -> {
                if (value != null) {
                    builder.queryParam(key, value);
                }
            });
        }

        // 设置请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            headers.forEach(httpHeaders::set);
        }

        // 创建请求实体
        HttpEntity<?> requestEntity = new HttpEntity<>(httpHeaders);

        // 发送GET请求
        log.info("发送GET请求，URL: {}", builder.toUriString());
        return restTemplate.exchange(
            builder.toUriString(),
            HttpMethod.GET,
            requestEntity,
            responseType
        );
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public static <T> ResponseEntity<T> post(String url, Object body,
                                          Map<String, String> headers, Class<T> responseType) {
        // 设置请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            headers.forEach(httpHeaders::set);
        }

        // 默认设置JSON内容类型
        if (!httpHeaders.containsKey(HttpHeaders.CONTENT_TYPE)) {
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        }

        // 创建请求实体
        HttpEntity<?> requestEntity = new HttpEntity<>(body, httpHeaders);

        // 发送POST请求
        log.info("发送POST请求，URL: {}", url);
        return restTemplate.exchange(
            url,
            HttpMethod.POST,
            requestEntity,
            responseType
        );
    }
}
