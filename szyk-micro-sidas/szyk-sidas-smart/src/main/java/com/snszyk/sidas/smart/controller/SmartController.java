package com.snszyk.sidas.smart.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.smart.annotation.ApiKeyAuth;
import com.snszyk.sidas.smart.service.logic.DiagnosisLogicService;
import com.snszyk.sidas.smart.service.logic.SmartLogicService;
import com.snszyk.sidas.smart.vo.MessageVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/smart")
@AllArgsConstructor
@Slf4j
public class SmartController {

	private final SmartLogicService smartLogicService;
	private final DiagnosisLogicService diagnosisLogicService;

	@PostMapping(path = "/process/request", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "dify请求接口", notes = "传入MessageVo")
	public SseEmitter processRequest(@RequestBody MessageVo v, HttpServletRequest request) {
		// 波形分析
		if (v.getMonitorId() != null) {
			String equipmentInfo = diagnosisLogicService.getEquipmentByMonitorId(v.getMonitorId());
			Map<String, Object> inputs = v.getInputs();
			if (inputs != null) {
				inputs.put("equipment", equipmentInfo);
			}
		}
		// 智能诊断机器人
		if (v.getDeviceId() != null || Func.isNotEmpty(v.getEquipmentIds())) {
			try {
				long start = System.currentTimeMillis();
				diagnosisLogicService.waveInfo(v.getDeviceId(), v.getEquipmentIds(), v.getDesc(), v.getInputs(), request);
				long end = System.currentTimeMillis();
				log.info("waveInfo耗时：{}s", (end - start) / 1000.0);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
		return smartLogicService.processRequest(v);
	}

	@ApiKeyAuth
	@GetMapping("/equipmentAlarm")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "设备告警信息", notes = "根据设备id获取设备告警信息")
	public String equipmentAlarm(@RequestParam(required = true) String equipmentId) {
		String s = diagnosisLogicService.equipmentAlarm(equipmentId);
		log.info("设备告警信息：{}", s);
		return s;
	}

	/**
	 * 获取dify通用问答流程历史会话记录
	 */
	@GetMapping("/chatflowHistory")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "获取dify通用问答流程历史会话记录", notes = "获取dify通用问答流程历史会话记录（默认20条）")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "type", value = "类型", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "lastId", value = "当前页最后面一条记录的 ID，默认 null", paramType = "query", dataType = "string", required = false),
		@ApiImplicitParam(name = "sortBy", value = "排序字段，默认 -updated_at(按更新时间倒序排列)，可选值：created_at, -created_at, updated_at, -updated_at，字段前面的符号代表顺序或倒序，-代表倒序",
			paramType = "query", dataType = "string", required = false),
		@ApiImplicitParam(name = "limit", value = "一次请求返回多少条记录，默认 20 条，最大 100 条，最小 1 条。", paramType = "query", dataType = "long", required = false)
	})
	public R chatflowHistory(@ApiIgnore @RequestParam Map<String, Object> map) {
		return R.data(smartLogicService.chatflowHistory(map));
	}
	/**
	 * 获取dify通用问答会话消息
	 */
	@GetMapping("/conversationMessage")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "获取dify通用问答会话消息", notes = "获取dify通用问答会话消息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "conversationId", value = "会话标识", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "type", value = "类型", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "firstId", value = "当前页第一条聊天记录的 ID，默认 null", paramType = "query", dataType = "string", required = false),
		@ApiImplicitParam(name = "limit", value = "一次请求返回多少条记录，默认 20 条。", paramType = "query", dataType = "long", required = false)
	})
	public R conversationMessage(@ApiIgnore @RequestParam Map<String, Object> map) {
		return R.data(smartLogicService.conversationMessage(map));
	}

	/**
	 * 获取dify通用问答会话消息
	 */
	@GetMapping("/del-conversation")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除会话消息", notes = "删除会话消息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "conversationId", value = "会话标识", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "type", value = "类型", paramType = "query", dataType = "string", required = true)
	})
	public R delConversation(@ApiIgnore @RequestParam Map<String, Object> map) {
		return R.status(smartLogicService.delConversation(map));
	}

	/**
	 * 获取下一步建议
	 */
	@GetMapping("/next-step-suggestion")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "获取下一步建议", notes = "获取下一步建议")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "messageId", value = "消息id", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "type", value = "类型", paramType = "query", dataType = "string", required = true)
	})
	public R nextStepSuggestion(@ApiIgnore @RequestParam Map<String,String> map) {
		try {
			return R.data(smartLogicService.nextStepSuggestion(map));
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
}
