package com.snszyk.sidas.smart.service.logic;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import com.snszyk.sidas.smart.service.ISmartMonthlyReportService;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 智能月报逻辑服务
 * 负责智能月报的业务逻辑处理，包括月报的查询、提交、详情获取等功能。
 * 集成附件服务，支持月报文件的上传和下载。
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
@Slf4j
public class SmartMonthlyReportLogicService {

	/** 智能月报服务 */
	private final ISmartMonthlyReportService smartMonthlyReportService;

	/** 附件客户端服务 */
	private final IAttachClient attachClient;

	public IPage<SmartMonthlyReportDTO> selectSmartMonthlyReportPage(IPage<SmartMonthlyReportDTO> page, SmartMonthlyReportVO smartMonthlyReportVO) {
		List<SmartMonthlyReportDTO> list = smartMonthlyReportService.selectSmartMonthlyReportPage(page, smartMonthlyReportVO);
		if (list != null) {
			list.forEach(dto -> {
				if (dto.getAttachId() != null) {
					R<Attach> attachR = attachClient.attachInfoById(dto.getAttachId());
					if (attachR.isSuccess() && attachR.getData() != null) {
						dto.setFile(attachR.getData());
					}
				}
			});
		}
		page.setRecords(list);
		return page;
	}

	public List<SmartMonthlyReportDTO> list(String fileName) {
		Wrapper<SmartMonthlyReport> queryWrapper = Wrappers.<SmartMonthlyReport>query().lambda().like(StringUtil.isNoneBlank(fileName), SmartMonthlyReport::getFileName, fileName);
		List<SmartMonthlyReport> list = smartMonthlyReportService.list(queryWrapper);
		if (list != null) {
			List<SmartMonthlyReportDTO> dtoList = Func.copy(list, SmartMonthlyReportDTO.class);
			dtoList.forEach(dto -> {
				if (dto.getAttachId() != null) {
					R<Attach> attachR = attachClient.attachInfoById(dto.getAttachId());
					if (attachR.isSuccess() && attachR.getData() != null) {
						dto.setFile(attachR.getData());
					}
				}
			});
			return dtoList;
		}
		return new ArrayList<>();
	}

	public boolean submit(SmartMonthlyReport report) {
		if (report.getAttachId() != null) {
			R<Attach> attachR = attachClient.attachInfoById(report.getAttachId());
			if (attachR.isSuccess() && attachR.getData() != null) {
				report.setFileName(attachR.getData().getOriginalName());
			}
		}
		return smartMonthlyReportService.saveOrUpdate(report);
	}

	public SmartMonthlyReportDTO detail(QueryWrapper<SmartMonthlyReport> queryWrapper) {
		SmartMonthlyReport report = smartMonthlyReportService.getOne(queryWrapper);
		if (report != null) {
			SmartMonthlyReportDTO dto = Func.copy(report, SmartMonthlyReportDTO.class);
			if (dto.getAttachId() != null) {
				R<Attach> attachR = attachClient.attachInfoById(dto.getAttachId());
				if (attachR.isSuccess() && attachR.getData() != null) {
					dto.setFile(attachR.getData());
				}
			}
			return dto;
		}
		return null;
	}
}
