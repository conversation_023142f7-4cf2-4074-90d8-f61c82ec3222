package com.snszyk.sidas.smart.aspect;

import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.sidas.smart.annotation.ApiKeyAuth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ApiKeyAuthAspect {

    @Value("${szyk.api-key.enabled:false}")
    private Boolean apiKeyEnabled;

    @Value("${szyk.api-key.value:}")
    private String apiKeyValue;

    @Value("${szyk.api-key.header-name:X-API-Key}")
    private String apiKeyHeaderName;

    @Before("@annotation(com.snszyk.sidas.smart.annotation.ApiKeyAuth)")
    public Object around(JoinPoint point) throws Throwable {
        // 如果API Key认证未启用，直接放行
        if (!apiKeyEnabled) {
            log.warn("API Key认证已禁用，但接口需要API Key认证");
			throw new ServiceException(ResultCode.FAILURE, new Exception("API Key认证已禁用"));
        }

        // 获取请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("无法获取请求上下文");
			throw new ServiceException(ResultCode.FAILURE, new Exception("系统错误"));
        }

        HttpServletRequest request = attributes.getRequest();
        String requestUri = request.getRequestURI();

        // 获取注解信息
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ApiKeyAuth apiKeyAuth = method.getAnnotation(ApiKeyAuth.class);
        String description = apiKeyAuth.value();

        // 获取并验证API Key
        String apiKey = request.getHeader(apiKeyHeaderName);
        if (apiKey == null || !apiKey.equals(apiKeyValue)) {
            log.warn("API Key认证失败: {}, 描述: {}", requestUri, description);
			throw new ServiceException(ResultCode.CLIENT_UN_AUTHORIZED, new Exception("Invalid API Key"));
        }

        log.debug("API Key认证成功: {}, 描述: {}", requestUri, description);
        return point.getArgs();
    }
}
