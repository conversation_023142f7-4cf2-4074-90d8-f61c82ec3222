#服务器端口
server:
  port: 8170
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: szyk_sidas
        group: SIDAS_GROUP
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        namespace: szyk_sidas
        group: SIDAS_GROUP

# API Key 认证配置 (可选)
#szyk:
#  api-key:
#    enabled: true
#    value: your-api-key-here
#    header-name: X-API-Key

