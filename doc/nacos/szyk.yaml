#服务器配置
server:
  undertow:
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true

#spring配置
spring:
  cloud:
    sentinel:
      eager: true
  devtools:
    restart:
      log-condition-evaluation-delta: false
    livereload:
      port: 23333

#feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

#hystrix配置
hystrix:
  threadpool:
    default:
      coreSize: 300
      maxQueueSize: 1000
      queueSizeRejectionThreshold: 800
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000

#ribbon配置
ribbon:
  #对当前实例的重试次数
  MaxAutoRetries: 1
  #切换实例的重试次数
  MaxAutoRetriesNextServer: 2
  #请求处理的超时时间
  ReadTimeout: 60000
  #请求连接的超时时间
  ConnectTimeout: 60000
  #对所有操作请求都进行重试
  OkToRetryOnAllOperations: true

#对外暴露端口
management:
  endpoints:
    enabled-by-default: false
#    web:
#      exposure:
#        include: "*"
  endpoint:
    health:
      show-details: always

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: szyk
    password: szyk
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2022 Szyk All Rights Reserved

#swagger公共信息
swagger:
  title: Szyk 接口文档系统
  description: Szyk 接口文档系统
  version: 1.3.0.RELEASE
  license: Powered By Szyk
  license-url: https://szyk.vip
  terms-of-service-url: https://szyk.vip
  contact:
    name: smallchill
    email: <EMAIL>
    url: https://gitee.com/smallc

#szyk配置
szyk:
  #token配置
  token:
    #是否有状态
    state: false
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr1USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: false
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /weixin
      - /notice/submit
  #安全框架配置
  secure:
    #接口放行
    skip-url:
      - /test/**
    #授权认证配置
    auth:
      - method: ALL
        pattern: /weixin/**
        expression: "hasAuth()"
      - method: POST
        pattern: /dashboard/upload
        expression: "hasTimeAuth(9, 17)"
      - method: POST
        pattern: /dashboard/submit
        expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      - method: ALL
        pattern: /dashboard/info
        username: "szyk"
        password: "szyk"
    #动态签名认证配置
    sign:
      - method: ALL
        pattern: /dashboard/sign
        crypto: "sha1"
    #多终端认证配置
    client:
      - client-id: sword
        path-patterns:
          - /sword/**
      - client-id: saber
        path-patterns:
          - /saber/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: true
    #多租户授权保护
    license: false
    #动态数据源功能
    dynamic-datasource: false
    #动态数据源全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
      - szyk_user

#mybatis-plus配置
mybatis-plus:
    global-config:
        #字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
        field-strategy: 0
