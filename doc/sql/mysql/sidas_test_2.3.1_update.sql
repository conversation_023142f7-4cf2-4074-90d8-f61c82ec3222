--------------------------------------
---       SiDAs v2.3.1 SQL修改     ---
--------------------------------------

-- 报警管理图谱表：新增“报警记录id”字段
ALTER TABLE `sidas_test`.`eolm_alarm_chart`
    ADD COLUMN `alarm_record_id` bigint(20) NULL COMMENT '报警记录id' AFTER `alarm_id`;

-- 新增：诊断报警记录关联表
CREATE TABLE `sidas_test`.`eolm_diagnosis_alarm_record` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `diagnosis_id` bigint(20) DEFAULT NULL COMMENT '诊断id',
    `alarm_record_id` bigint(20) DEFAULT NULL COMMENT '报警记录id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诊断报警记录关联表';

-- 故障管理业务表：新增“故障案例编号”字段
ALTER TABLE `sidas_test`.`eolm_fault_biz`
    ADD COLUMN `case_no` varchar(255) NULL COMMENT '故障案例编号' AFTER `is_case`;

-- 新增：诊断报告表
CREATE TABLE `sidas_test`.`eolm_diagnostic_report` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `diagnosis_id` bigint(20) NOT NULL COMMENT '诊断id',
    `content` longtext COMMENT '报告内容',
    `status` int(2) DEFAULT '0' COMMENT '状态',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诊断报告表';

-- 图谱数据表新增 速度波形 字段
ALTER TABLE `sidas_test`.`eolm_sensor_spectrum_graph`
    ADD COLUMN `velocity_waveform` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '速度波形' AFTER `cepstrum_waveform`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`) USING BTREE;

-- 增加厂区采集站关联表
CREATE TABLE `eolm_device_collection_station` (
     `id` bigint(20) NOT NULL COMMENT '自增主键',
     `device_id` bigint(20) DEFAULT NULL COMMENT '厂区id',
     `station_id` bigint(20) DEFAULT NULL COMMENT '采集站id',
     `coordinate` text COMMENT '采集站在厂区模型图的坐标',
     `status` int(2) DEFAULT '0' COMMENT '状态',
     `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
     `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='厂区采集站关联表';

-- 修改传感器数据相关表的采样频率字段精确位数、增加采样点数
ALTER TABLE `sidas_test`.`eolm_sensor_raw_data`
    MODIFY COLUMN `sampling_freq` decimal(10, 3) NULL DEFAULT NULL COMMENT '采样频率' AFTER `sampling_time`,
    ADD COLUMN `sampling_points` int(11) NULL DEFAULT NULL COMMENT '采样点数' AFTER `sampling_freq`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`) USING BTREE;

ALTER TABLE `sidas_test`.`eolm_sensor_spectrum_graph`
    MODIFY COLUMN `sampling_freq` decimal(10, 3) NULL DEFAULT NULL COMMENT '采样频率' AFTER `wave_config_name`,
    ADD COLUMN `sampling_points` int(11) NULL DEFAULT NULL COMMENT '采样点数' AFTER `sampling_freq`,
    ADD COLUMN `cutoff_freq` decimal(10,2) DEFAULT NULL COMMENT '截止频率' AFTER `sampling_freq`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`) USING BTREE;

-- 测点表：新增“诊断图片”字段
ALTER TABLE `sidas_test`.`eolm_monitor`
    ADD COLUMN `diagnostic_image` varchar(255) NULL COMMENT '诊断图片' AFTER `alarm_path`;

-- 3D场景表：新增“模型id集合”字段
ALTER TABLE `sidas_test`.`eolm_device_scene`
    ADD COLUMN `model_id` varchar(255) NULL COMMENT '模型id集合' AFTER `name`;

-- 报警管理图谱表：新增“数据采集时间”字段
ALTER TABLE `sidas_test`.`eolm_alarm_chart`
    ADD COLUMN `origin_time` datetime NULL COMMENT '数据采集时间' AFTER `envelop_diagram`;

-- 传感器参数表：新增“采样点数”字段
ALTER TABLE `sidas_test`.`eolm_sensor_param`
    ADD COLUMN `sampling_points` int(11) NULL COMMENT '采样点数' AFTER `sampling_freq`;

-- IOT数据采集配置表：新增“采样点数”字段
ALTER TABLE `sidas_test`.`eolm_daq_config`
    ADD COLUMN `sampling_points` int(11) NULL COMMENT '采样点数' AFTER `sampling_freq`;

-- 波形配置表：新增“采样点数”字段
ALTER TABLE `sidas_test`.`eolm_wave_config`
    ADD COLUMN `sampling_points` int(11) NULL COMMENT '采样点数' AFTER `wave_config_name`;

-- 新增：全局配置表
CREATE TABLE `eolm_global_config` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户id',
    `category` varchar(100) DEFAULT NULL COMMENT '配置类型',
    `settings` varchar(1000) DEFAULT NULL COMMENT '配置信息',
    `status` int(2) DEFAULT '0' COMMENT '状态',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    `is_deleted` int(2) DEFAULT NULL COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局配置表';

-- 新增：版本控制表
CREATE TABLE `szyk_version` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `latest_version` varchar(32) DEFAULT NULL COMMENT '推送更新版本号',
    `lowest_version` varchar(32) DEFAULT NULL COMMENT '最低兼容版本号',
    `content` varchar(2000) DEFAULT NULL COMMENT '迭代内容',
    `publish_time` datetime DEFAULT NULL COMMENT '版本发布时间',
    `publish_user` bigint(20) DEFAULT NULL COMMENT '发布人',
    `latest_publish_time` datetime DEFAULT NULL COMMENT '最终发布时间',
    `ios_download` varchar(255) DEFAULT NULL COMMENT 'ios下载地址',
    `android_download` varchar(255) DEFAULT NULL COMMENT '安卓下载地址',
    `hot_update` bigint(20) DEFAULT NULL COMMENT '热更新id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Update table szyk_message
-- ----------------------------
ALTER TABLE szyk_message ADD COLUMN `is_immediate` int(2) NULL COMMENT '立即发送：1-否；2-是' AFTER `read_callback_url`;
ALTER TABLE szyk_message ADD COLUMN `channel` varchar(50) NULL COMMENT '发送渠道：PC、APP、MINI_PROGRAM' AFTER `schedule_time`;
ALTER TABLE szyk_message ADD COLUMN `receiver_type` varchar(50) NULL COMMENT '接收人类型：USER、DEPT、ROLE' AFTER `channel`;
ALTER TABLE szyk_message ADD COLUMN `receiver_info` text NULL COMMENT '接收人信息：json字符串' AFTER `receiver_type`;
ALTER TABLE szyk_message MODIFY COLUMN `schedule_time` datetime NULL COMMENT '定时发送时间';

-- ----------------------------
-- Table structure for szyk_user_role
-- ----------------------------
CREATE TABLE `szyk_user_role` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `user_id` bigint(20) DEFAULT '0' COMMENT '用户ID',
    `role_id` bigint(20) DEFAULT '0' COMMENT '角色ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';

-- ----------------------------
-- Update table szyk_message_push
-- ----------------------------
ALTER TABLE szyk_message_push ADD COLUMN `type` varchar(20) NULL COMMENT '消息类型' AFTER `receiver`;

ALTER TABLE `sidas_test`.`szyk_message_push`
ADD COLUMN `biz_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型' AFTER `type`,
ADD COLUMN `biz_id` bigint(20) NULL DEFAULT NULL COMMENT '业务主键' AFTER `biz_type`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`) USING BTREE;

-- ----------------------------
-- Update table szyk_message：增加业务类型和业务id
-- ----------------------------
ALTER TABLE `sidas_test`.`szyk_message`
    ADD COLUMN `biz_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '业务类型' AFTER `type`,
ADD COLUMN `biz_id` bigint(20) NULL COMMENT '业务主键' AFTER `biz_type`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`) USING BTREE;

-- ----------------------------
-- 修改采样时间的精确位数位4
-- ----------------------------
ALTER TABLE `sidas_test`.`eolm_daq_config`
MODIFY COLUMN `sampling_time` decimal(10, 4) NULL DEFAULT NULL COMMENT '采样时间' AFTER `range_max`;

ALTER TABLE `sidas_test`.`eolm_sensor_param`
MODIFY COLUMN `sampling_time` decimal(10, 4) NULL DEFAULT NULL COMMENT '采样时间' AFTER `sampling_points`;

ALTER TABLE `sidas_test`.`eolm_sensor_raw_data`
MODIFY COLUMN `sampling_time` decimal(10, 4) NULL DEFAULT NULL COMMENT '采样时间' AFTER `range_max`;

ALTER TABLE `sidas_test`.`eolm_sensor_spectrum_graph`
MODIFY COLUMN `sampling_time` decimal(10, 4) NULL DEFAULT NULL COMMENT '采样时间' AFTER `sampling_points`;

ALTER TABLE `sidas_test`.`eolm_wave_config`
MODIFY COLUMN `sampling_time` decimal(10, 4) NULL DEFAULT NULL COMMENT '采样时间' AFTER `cutoff_freq`;

ALTER TABLE `sidas_test`.`eolm_sensor_spectrum_graph`
ADD COLUMN `cutoff_freq` decimal(10, 2) NULL COMMENT '截止频率' AFTER `sampling_freq`;

ALTER TABLE `sidas_test`.`szyk_attach`
MODIFY COLUMN `tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '租户ID' AFTER `id`;
