-- -----------------------------------
-- 新增lemon平台所需的组件地址字段
-- -----------------------------------
ALTER TABLE "SZYK"."SZYK_MENU"
    ADD ("COMPONENT" VARCHAR2(255) );

COMMENT ON COLUMN "SZYK"."SZYK_MENU"."COMPONENT" IS '组件地址';

-- -----------------------------------
-- 参数管理新增账号错误锁定次数配置
-- -----------------------------------
INSERT INTO "SZYK"."SZYK_PARAM"("ID", "PARAM_NAME", "PARAM_KEY", "PARAM_VALUE", "REMARK", "CREATE_USER", "CREATE_DEPT", "CREATE_TIME", "UPDATE_USER", "UPDATE_TIME", "STATUS", "IS_DELETED") VALUES ('1123598819738675203', '账号错误锁定次数', 'account.failCount', '5', '锁定次数', '1123598821738675201', '1123598813738675201', TO_DATE('2021-12-01 12:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1123598821738675201', TO_DATE('2021-12-01 12:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1', '0');

-- -----------------------------------
-- 创建租户产品表
-- -----------------------------------
CREATE TABLE "SZYK"."SZYK_TENANT_PACKAGE" (
    "ID" NUMBER(20) NOT NULL ,
    "PACKAGE_NAME" NVARCHAR2(50) ,
    "MENU_ID" NVARCHAR2(2000) ,
    "REMARK" NVARCHAR2(255) ,
    "CREATE_USER" NUMBER(20) ,
    "CREATE_DEPT" NUMBER(20) ,
    "CREATE_TIME" DATE ,
    "UPDATE_USER" NUMBER(20) ,
    "UPDATE_TIME" DATE ,
    "STATUS" NUMBER(11) ,
    "IS_DELETED" NUMBER(11) ,
    PRIMARY KEY ("ID")
)
;

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."ID" IS '主键';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."PACKAGE_NAME" IS '产品包名';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."MENU_ID" IS '菜单ID';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."REMARK" IS '备注';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."CREATE_USER" IS '创建人';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."CREATE_DEPT" IS '创建部门';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."UPDATE_USER" IS '修改人';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."UPDATE_TIME" IS '修改时间';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."STATUS" IS '状态';

COMMENT ON COLUMN "SZYK"."SZYK_TENANT_PACKAGE"."IS_DELETED" IS '是否已删除';

COMMENT ON TABLE "SZYK"."SZYK_TENANT_PACKAGE" IS '租户产品表';

-- -----------------------------------
-- 租户表增加产品包ID字段
-- -----------------------------------
ALTER TABLE "SZYK"."SZYK_TENANT"
    ADD ("PACKAGE_ID" NUMBER(20) );

COMMENT ON COLUMN "SZYK"."SZYK_TENANT"."PACKAGE_ID" IS '产品包ID';
